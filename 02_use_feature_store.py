#!/usr/bin/env python3
"""
特征工程和模型数据准备系统
从原始股票数据计算技术指标，生成机器学习训练数据
采用高效的pandas数据处理，确保跨平台兼容性
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import glob
from pathlib import Path
import warnings

warnings.filterwarnings('ignore')

# =============================================================================
# 技术指标计算函数
# =============================================================================

def calculate_sma(data, window):
    """计算简单移动平均"""
    return data.rolling(window=window).mean()

def calculate_ema(data, window):
    """计算指数移动平均"""
    return data.ewm(span=window, adjust=False).mean()

def calculate_macd(data, fast=12, slow=26, signal=9):
    """计算MACD指标"""
    ema_fast = calculate_ema(data, fast)
    ema_slow = calculate_ema(data, slow)
    macd = ema_fast - ema_slow
    macd_signal = calculate_ema(macd, signal)
    macd_histogram = macd - macd_signal
    return macd, macd_signal, macd_histogram

def calculate_rsi(data, window=14):
    """计算RSI指标"""
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_bollinger_bands(data, window=20, num_std=2):
    """计算布林带"""
    sma = calculate_sma(data, window)
    std = data.rolling(window=window).std()
    upper_band = sma + (std * num_std)
    lower_band = sma - (std * num_std)
    return upper_band, sma, lower_band

def calculate_atr(high, low, close, window=14):
    """计算平均真实范围"""
    high_low = high - low
    high_close = np.abs(high - close.shift())
    low_close = np.abs(low - close.shift())
    
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = ranges.max(axis=1)
    
    return true_range.rolling(window=window).mean()

def calculate_volume_weighted_average_price(df):
    """计算成交量加权平均价格"""
    return (df['close'] * df['volume']).sum() / df['volume'].sum()

def calculate_relative_strength(data, period=14):
    """计算相对强弱指数"""
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_momentum(data, period=10):
    """计算动量指标"""
    return data.pct_change(periods=period)

def calculate_volatility(data, period=20):
    """计算波动率"""
    return data.pct_change().rolling(window=period).std()

def calculate_price_position(close, high, low, period=14):
    """计算价格位置"""
    highest_high = high.rolling(window=period).max()
    lowest_low = low.rolling(window=period).min()
    return (close - lowest_low) / (highest_high - lowest_low)

def calculate_comprehensive_features(df):
    """计算综合技术指标 - 专业级量化特征工程"""

    # 基础特征
    df['ohlc_avg'] = (df['open'] + df['high'] + df['low'] + df['close']) / 4
    df['hl_avg'] = (df['high'] + df['low']) / 2
    df['price_change'] = df['close'].pct_change()
    df['volume_change'] = df['volume'].pct_change()

    # 趋势指标 - 优化移动平均线组合
    df['sma_5'] = calculate_sma(df['close'], 5)
    df['sma_10'] = calculate_sma(df['close'], 10)
    df['sma_20'] = calculate_sma(df['close'], 20)
    df['sma_55'] = calculate_sma(df['close'], 55)  # 改为55日均线
    df['sma_233'] = calculate_sma(df['close'], 233)  # 新增233日均线
    df['ema_12'] = calculate_ema(df['close'], 12)
    df['ema_26'] = calculate_ema(df['close'], 26)

    # 价格距离均线特征 - 新增专业特征
    df['close_to_sma55_dist'] = df['close'] - df['sma_55']  # 收盘价距离55日均线
    df['close_to_sma233_dist'] = df['close'] - df['sma_233']  # 收盘价距离233日均线
    df['close_to_sma55_pct'] = (df['close'] - df['sma_55']) / df['sma_55']  # 百分比距离
    df['close_to_sma233_pct'] = (df['close'] - df['sma_233']) / df['sma_233']  # 百分比距离
    
    # MACD
    macd, macd_signal, macd_histogram = calculate_macd(df['close'])
    df['macd'] = macd
    df['macd_signal'] = macd_signal
    df['macd_histogram'] = macd_histogram
    
    # RSI - 主要RSI指标
    df['rsi'] = calculate_rsi(df['close'], 14)  # 保留标准14日RSI

    # 布林带
    bb_upper, bb_middle, bb_lower = calculate_bollinger_bands(df['close'])
    df['bb_upper'] = bb_upper
    df['bb_middle'] = bb_middle
    df['bb_lower'] = bb_lower

    # ATR - 波动率指标
    df['atr'] = calculate_atr(df['high'], df['low'], df['close'])
    df['atr_ratio'] = df['atr'] / df['close']  # ATR相对比率

    # 成交量指标
    df['volume_sma'] = calculate_sma(df['volume'], 20)
    df['volume_ema'] = calculate_ema(df['volume'], 10)  # 新增成交量EMA

    # 高级特征
    df['price_momentum'] = calculate_momentum(df['close'], 10)
    df['volatility'] = calculate_volatility(df['close'], 20)
    df['price_position'] = calculate_price_position(df['close'], df['high'], df['low'], 14)
    
    # 价格区间
    df['price_range'] = df['high'] - df['low']
    df['price_range_pct'] = df['price_range'] / df['close']
    
    # 成交量相关
    df['volume_price_trend'] = (df['close'] - df['close'].shift(1)) * df['volume']
    
    # 均线差异和趋势强度 - 优化均线组合
    df['sma_diff_20_55'] = df['sma_20'] - df['sma_55']  # 更新为55日均线
    df['sma_diff_55_233'] = df['sma_55'] - df['sma_233']  # 新增长期趋势
    df['ema_diff_12_26'] = df['ema_12'] - df['ema_26']

    # 均线排列强度 - 新增专业特征
    df['ma_alignment_strength'] = (
        (df['sma_5'] > df['sma_10']).astype(int) +
        (df['sma_10'] > df['sma_20']).astype(int) +
        (df['sma_20'] > df['sma_55']).astype(int) +
        (df['sma_55'] > df['sma_233']).astype(int)
    ) / 4  # 标准化到0-1范围

    # 均线斜率 - 趋势方向强度
    df['sma_55_slope'] = df['sma_55'].pct_change(5)  # 55日均线5日斜率
    df['sma_233_slope'] = df['sma_233'].pct_change(10)  # 233日均线10日斜率
    
    # 布林带位置
    df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # 交易强度
    df['trading_intensity'] = df['volume'] / df['volume_sma']

    # 新增专业量化特征
    # 1. 多时间框架RSI
    df['rsi_5'] = calculate_rsi(df['close'], 5)   # 短期RSI
    df['rsi_21'] = calculate_rsi(df['close'], 21) # 中期RSI

    # 2. 价格动量指标
    df['momentum_5'] = calculate_momentum(df['close'], 5)
    df['momentum_20'] = calculate_momentum(df['close'], 20)
    df['momentum_60'] = calculate_momentum(df['close'], 60)

    # 3. 成交量价格确认指标
    df['volume_price_confirmation'] = np.where(
        (df['price_change'] > 0) & (df['volume_change'] > 0), 1,
        np.where((df['price_change'] < 0) & (df['volume_change'] > 0), -1, 0)
    )

    # 4. 突破信号
    df['breakout_signal'] = np.where(
        df['close'] > df['bb_upper'], 1,
        np.where(df['close'] < df['bb_lower'], -1, 0)
    )

    # 5. 趋势一致性指标
    df['trend_consistency'] = (
        (df['sma_5'] > df['sma_10']).astype(int) +
        (df['ema_12'] > df['ema_26']).astype(int) +
        (df['macd'] > df['macd_signal']).astype(int) +
        (df['rsi'] > 50).astype(int)
    ) / 4

    # 6. 波动率调整收益
    df['volatility_adjusted_return'] = df['price_change'] / (df['volatility'] + 1e-8)

    return df

class FeatureManager:
    """专业级特征工程管理器"""
    
    def __init__(self):
        self.data_dir = Path("data")
        self.model_data_dir = Path("model/data")
        self.model_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 时间分割配置
        self.train_end = "2022-12-31"
        self.test_start = "2023-01-01"
        
    def load_stock_data(self, symbols: list = None, limit_days: int = None):
        """
        从Parquet数据中加载股票数据并计算技术指标
        """
        print("=== 从Parquet数据加载股票数据 ===")
        
        if not self.data_dir.exists():
            print("❌ 数据目录不存在，请先运行 01_fetch_and_save_data.py")
            return pd.DataFrame()
        
        # 查找所有parquet文件
        parquet_files = []
        timeframe_dir = self.data_dir / "timeframe=1d"
        
        if timeframe_dir.exists():
            for symbol_dir in timeframe_dir.iterdir():
                if symbol_dir.is_dir() and symbol_dir.name.startswith("symbol="):
                    symbol = symbol_dir.name.replace("symbol=", "")
                    
                    # 过滤指定的股票
                    if symbols and symbol not in symbols:
                        continue
                        
                    parquet_file = symbol_dir / "data.parquet"
                    if parquet_file.exists():
                        parquet_files.append((symbol, parquet_file))
        
        if not parquet_files:
            print("❌ 未找到任何Parquet文件")
            return pd.DataFrame()
        
        print(f"找到 {len(parquet_files)} 个数据文件，开始加载...")
        
        # 分批加载数据，避免内存问题
        all_data = []
        batch_size = 50  # 每批处理50个文件
        
        for i in range(0, len(parquet_files), batch_size):
            batch_files = parquet_files[i:i+batch_size]
            batch_data = []
            
            for symbol, file_path in batch_files:
                try:
                    df = pd.read_parquet(file_path)
                    if not df.empty:
                        # 确保有symbol列
                        df['symbol'] = symbol
                        
                        # 计算完整的技术指标
                        df = calculate_comprehensive_features(df)
                        
                        batch_data.append(df)
                        print(f"  加载 {symbol}: {len(df)} 条记录")
                except Exception as e:
                    print(f"  ⚠️ 跳过 {symbol}: {e}")
                    continue
            
            if batch_data:
                batch_df = pd.concat(batch_data, ignore_index=True)
                all_data.append(batch_df)
                print(f"批次 {i//batch_size + 1} 完成，加载 {len(batch_data)} 个文件")
        
        if not all_data:
            print("❌ 没有成功加载任何数据")
            return pd.DataFrame()
        
        # 合并所有数据
        print("合并所有数据...")
        df = pd.concat(all_data, ignore_index=True)
        
        # 应用时间过滤
        if limit_days:
            latest_date = df['timestamp'].max()
            start_date = pd.to_datetime(latest_date) - timedelta(days=limit_days)
            df = df[df['timestamp'] >= start_date]
            print(f"应用时间过滤：最近 {limit_days} 天")
        
        # 重命名timestamp为date以兼容model目录
        df = df.rename(columns={'timestamp': 'date'})
        
        # 确保日期格式正确
        df['date'] = pd.to_datetime(df['date'])
        
        # 排序
        df = df.sort_values(['date', 'symbol']).reset_index(drop=True)
        
        print(f"✅ 成功加载数据:")
        print(f"  - 总行数: {len(df):,}")
        print(f"  - 股票数量: {df['symbol'].nunique()}")
        print(f"  - 日期范围: {df['date'].min().date()} 到 {df['date'].max().date()}")
        
        return df
    
    def get_sp500_top_symbols(self, top_n: int = 100):
        """获取成交量最大的前N个股票代码"""
        print(f"自动选择成交量最大的 {top_n} 只股票...")
        
        # 获取所有可用的股票代码
        timeframe_dir = self.data_dir / "timeframe=1d"
        if not timeframe_dir.exists():
            return []
        
        volume_data = []
        for symbol_dir in timeframe_dir.iterdir():
            if symbol_dir.is_dir() and symbol_dir.name.startswith("symbol="):
                symbol = symbol_dir.name.replace("symbol=", "")
                parquet_file = symbol_dir / "data.parquet"
                
                if parquet_file.exists():
                    try:
                        df = pd.read_parquet(parquet_file)
                        if not df.empty and 'volume' in df.columns:
                            # 计算平均成交量
                            recent_data = df[df['timestamp'] >= '2023-01-01']
                            if len(recent_data) > 200:  # 确保有足够的数据点
                                avg_volume = recent_data['volume'].mean()
                                volume_data.append({'symbol': symbol, 'avg_volume': avg_volume})
                    except Exception:
                        continue
        
        if not volume_data:
            # 如果无法计算成交量，返回前N个可用股票
            symbols = []
            for symbol_dir in timeframe_dir.iterdir():
                if symbol_dir.is_dir() and symbol_dir.name.startswith("symbol="):
                    symbol = symbol_dir.name.replace("symbol=", "")
                    symbols.append(symbol)
                    if len(symbols) >= top_n:
                        break
            return symbols
        
        # 按成交量排序
        volume_df = pd.DataFrame(volume_data).sort_values('avg_volume', ascending=False)
        return volume_df['symbol'].head(top_n).tolist()
    
    def generate_target_labels(self, df: pd.DataFrame, future_days: int = 10, threshold: float = 0.05):
        """生成统一的交易标签（目标变量）"""
        print(f"\n=== 生成交易标签 ===")
        print(f"参数: 未来{future_days}日, 阈值{threshold:.1%}")
        
        data = df.copy()
        data = data.sort_values(['symbol', 'date']).reset_index(drop=True)
        
        # 计算未来收益率：(future_price - current_price) / current_price
        data['future_return'] = data.groupby('symbol')['close'].transform(
            lambda x: (x.shift(-future_days) - x) / x
        )
        
        # 生成二分类标签
        data['target'] = (data['future_return'] > threshold).astype(int)
        
        # 移除无法计算标签的行（最后N天没有未来数据）
        data = data.dropna(subset=['future_return', 'target'])
        
        # 统计标签分布
        label_stats = data['target'].value_counts()
        pos_ratio = data['target'].mean()
        
        print(f"✅ 标签生成完成:")
        print(f"  标签分布: {dict(label_stats)}")
        print(f"  正例比例: {pos_ratio:.3f}")
        print(f"  有效样本: {len(data):,}")
        
        # 数据质量检查
        if pos_ratio < 0.01 or pos_ratio > 0.99:
            print(f"⚠️ 警告：标签分布极不均衡，可能存在数据问题")
        
        return data

    def clean_features(self, df: pd.DataFrame):
        """统一的特征清洗和预处理"""
        print(f"\n=== 特征清洗和预处理 ===")
        
        data = df.copy()
        original_rows = len(data)
        
        # 1. 特征列定义
        feature_columns = [col for col in data.columns if col not in ['date', 'symbol', 'target', 'future_return']]
        
        # 2. 处理缺失值
        before_na = data[feature_columns].isnull().sum().sum()
        for col in feature_columns:
            # 按股票分组进行前向填充和后向填充
            data[col] = data.groupby('symbol')[col].transform(
                lambda x: x.fillna(method='ffill').fillna(method='bfill')
            )
            # 如果仍有缺失值，使用该特征的中位数填充
            if data[col].isnull().sum() > 0:
                median_value = data[col].median()
                data[col] = data[col].fillna(median_value)
        print(f"  处理缺失值: {before_na:,} 个")
        
        # 3. 处理无穷大值
        inf_count = 0
        for col in feature_columns:
            if data[col].dtype in ['float64', 'float32']:
                inf_mask = np.isinf(data[col])
                inf_count += inf_mask.sum()
                if inf_count > 0:
                    # 使用99.9%分位数替换正无穷，0.1%分位数替换负无穷
                    upper_bound = data[col][~inf_mask].quantile(0.999)
                    lower_bound = data[col][~inf_mask].quantile(0.001)
                    data[col] = data[col].replace([np.inf], upper_bound)
                    data[col] = data[col].replace([-np.inf], lower_bound)
        print(f"  处理无穷大值: {inf_count:,} 个")
        
        # 4. 处理极端异常值
        outlier_count = 0
        for col in feature_columns:
            if data[col].dtype in ['float64', 'float32', 'int64', 'int32']:
                mean_val = data[col].mean()
                std_val = data[col].std()
                if std_val > 0:
                    upper_bound = mean_val + 10 * std_val
                    lower_bound = mean_val - 10 * std_val
                    
                    outlier_mask = (data[col] > upper_bound) | (data[col] < lower_bound)
                    outlier_count += outlier_mask.sum()
                    
                    data[col] = data[col].clip(lower_bound, upper_bound)
        
        print(f"  处理极端异常值: {outlier_count:,} 个")
        
        # 5. 移除包含target缺失值的行
        if 'target' in data.columns:
            before_target = len(data)
            data = data.dropna(subset=['target'])
            target_dropped = before_target - len(data)
            print(f"  移除target缺失行: {target_dropped:,} 行")
        
        final_rows = len(data)
        print(f"✅ 特征清洗完成: {original_rows:,} → {final_rows:,} 行")
        
        return data

    def split_train_test_data(self, df: pd.DataFrame):
        """分割训练和测试数据"""
        print("\n=== 分割训练和测试数据 ===")
        
        # 分割数据
        train_data = df[df['date'] <= self.train_end].copy()
        test_data = df[df['date'] >= self.test_start].copy()
        
        print(f"训练数据: {len(train_data):,} 条 ({train_data['date'].min().date()} 到 {train_data['date'].max().date()})")
        print(f"测试数据: {len(test_data):,} 条 ({test_data['date'].min().date()} 到 {test_data['date'].max().date()})")
        
        # 显示标签分布
        if 'target' in train_data.columns:
            train_pos = train_data['target'].mean()
            test_pos = test_data['target'].mean()
            print(f"训练集正例比例: {train_pos:.3f}")
            print(f"测试集正例比例: {test_pos:.3f}")
        
        return train_data, test_data
    

    
    def save_model_data(self, train_data: pd.DataFrame, test_data: pd.DataFrame):
        """保存模型训练数据"""
        print("\n=== 保存模型数据 ===")
        
        # 保存为Parquet格式供模型使用
        train_file = self.model_data_dir / "training_data.parquet"
        test_file = self.model_data_dir / "test_data.parquet"
        
        train_data.to_parquet(train_file, index=False)
        test_data.to_parquet(test_file, index=False)
        
        print(f"✅ 训练数据已保存: {train_file}")
        print(f"✅ 测试数据已保存: {test_file}")
        
        # 显示数据统计
        print(f"\n数据统计:")
        print(f"  训练数据: {len(train_data):,} 条, {train_data['symbol'].nunique()} 只股票")
        print(f"  测试数据: {len(test_data):,} 条, {test_data['symbol'].nunique()} 只股票")
        
        # 检查特征列
        feature_columns = [col for col in train_data.columns if col not in ['date', 'symbol']]
        print(f"  可用特征: {len(feature_columns)} 个")
        
        # 显示特征列表
        print(f"  特征列表: {sorted(feature_columns)}")
        
        if len(feature_columns) < 15:
            print("⚠️ 警告：可用特征较少，可能影响模型性能")
    
    def run_feature_pipeline(self, symbols: list = None):
        """运行完整的特征工程管道"""
        print("=== 运行特征工程和数据准备流程 ===\n")

        # 1. 加载数据
        if symbols is None:
            symbols = self.get_sp500_top_symbols(100)
            print(f"自动选择成交量最大的100只股票")

        df = self.load_stock_data(symbols)
        if df.empty:
            return False

        # 2. 生成交易标签
        df = self.generate_target_labels(df)

        # 3. 特征清洗
        df = self.clean_features(df)

        # 4. 分割训练测试数据
        train_data, test_data = self.split_train_test_data(df)

        # 5. 保存模型数据
        self.save_model_data(train_data, test_data)

        print(f"\n✅ 特征工程流程完成!")
        print(f"现在可以运行: python 03_walk_forward_analysis.py")

        return True

def main():
    """主函数"""
    import sys

    print("=== 特征工程和模型数据准备系统 ===\n")

    # 检查01脚本的数据
    if not Path("data").exists():
        print("❌ 未找到数据目录，请先运行: python 01_fetch_and_save_data.py")
        return

    # 创建特征管理器
    feature_manager = FeatureManager()

    # 解析命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg == "--auto":
            print("🤖 自动化模式：为模型准备数据（标准流程）")
            feature_manager.run_feature_pipeline()
        elif arg == "--auto-quality":
            print("🎯 自动化模式：高质量股票筛选（250只）")
            symbols = feature_manager.get_sp500_top_symbols(250)
            feature_manager.run_feature_pipeline(symbols)
        elif arg == "--auto-all":
            print("📊 自动化模式：全量股票处理（517只）")
            symbols = feature_manager.get_sp500_top_symbols(517)
            feature_manager.run_feature_pipeline(symbols)
        else:
            print(f"❌ 未知参数: {arg}")
            print("支持的参数: --auto, --auto-quality, --auto-all")
            return
    else:
        # 交互模式
        print("请选择操作:")
        print("1. 标准流程 - 为模型准备数据（100只股票，快速）")
        print("2. 高质量模式 - 筛选优质股票（250只股票，推荐）")
        print("3. 全量模式 - 处理所有股票（517只股票，完整）")
        print("4. 自定义股票列表")

        choice = input("\n请输入选择 (1-4): ").strip()

        if choice == "1":
            print("🚀 运行标准流程...")
            feature_manager.run_feature_pipeline()
        elif choice == "2":
            print("🎯 运行高质量模式...")
            symbols = feature_manager.get_sp500_top_symbols(250)
            feature_manager.run_feature_pipeline(symbols)
        elif choice == "3":
            print("📊 运行全量模式...")
            symbols = feature_manager.get_sp500_top_symbols(517)
            feature_manager.run_feature_pipeline(symbols)
        elif choice == "4":
            symbols_input = input("请输入股票代码（用逗号分隔，如AAPL,MSFT,NVDA）: ").strip()
            if symbols_input:
                symbols = [s.strip().upper() for s in symbols_input.split(',')]
                feature_manager.run_feature_pipeline(symbols)
            else:
                print("无效输入，使用标准流程")
                feature_manager.run_feature_pipeline()
        else:
            print("无效选择，运行标准流程")
            feature_manager.run_feature_pipeline()

if __name__ == "__main__":
    main()