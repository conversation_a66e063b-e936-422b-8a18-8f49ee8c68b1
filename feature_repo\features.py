"""
Feast特征定义文件
定义股票数据的特征视图和实体
"""

from datetime import timedelta
from feast import Entity, FeatureView, Field, FileSource, ValueType
from feast.types import Float64, String, Int64

# 1. 定义数据源
stock_data_source = FileSource(
    name="stock_data_source",
    path="data/",  # 指向包含所有分区数据的根目录
    timestamp_field="timestamp",
    description="股票OHLC数据源，包含计算后的技术指标特征",
)

# 2. 定义实体
stock_entity = Entity(
    name="stock_symbol",
    value_type=ValueType.STRING,
    join_keys=["symbol"],
    description="股票代码实体",
)

# 3. 定义特征视图
stock_daily_features = FeatureView(
    name="stock_daily_features",
    entities=[stock_entity],
    ttl=timedelta(days=7),  # 特征存活时间，7天
    schema=[
        # 基础价格数据
        Field(name="open", dtype=Float64, description="开盘价"),
        Field(name="high", dtype=Float64, description="最高价"),
        Field(name="low", dtype=Float64, description="最低价"),
        Field(name="close", dtype=Float64, description="收盘价"),
        Field(name="volume", dtype=Int64, description="成交量"),
        
        # 基础计算特征
        Field(name="ohlc_avg", dtype=Float64, description="OHLC平均价"),
        Field(name="hl_avg", dtype=Float64, description="最高最低价平均"),
        Field(name="price_change", dtype=Float64, description="价格变化率"),
        Field(name="volume_change", dtype=Float64, description="成交量变化率"),
        
        # 技术指标
        Field(name="macd", dtype=Float64, description="MACD指标"),
        Field(name="macd_signal", dtype=Float64, description="MACD信号线"),
        Field(name="macd_histogram", dtype=Float64, description="MACD柱状图"),
        Field(name="rsi", dtype=Float64, description="RSI相对强弱指数"),
        
        # 移动平均线
        Field(name="sma_20", dtype=Float64, description="20日简单移动平均"),
        Field(name="sma_50", dtype=Float64, description="50日简单移动平均"),
        Field(name="ema_12", dtype=Float64, description="12日指数移动平均"),
        Field(name="ema_26", dtype=Float64, description="26日指数移动平均"),
        
        # 布林带
        Field(name="bb_upper", dtype=Float64, description="布林带上轨"),
        Field(name="bb_middle", dtype=Float64, description="布林带中轨"),
        Field(name="bb_lower", dtype=Float64, description="布林带下轨"),
        
        # 其他指标
        Field(name="atr", dtype=Float64, description="平均真实范围"),
        Field(name="volume_sma", dtype=Float64, description="成交量移动平均"),
        Field(name="price_momentum", dtype=Float64, description="价格动量"),
        Field(name="volatility", dtype=Float64, description="波动性"),
    ],
    online=True,
    source=stock_data_source,
    tags={"timeframe": "daily", "asset_class": "equity"},
) 