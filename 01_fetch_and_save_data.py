#!/usr/bin/env python3
"""
美股数据智能增量更新系统
从2015年1月1日开始获取美股历史数据，智能检查本地数据状态，只下载新数据避免重复
支持前复权数据格式，计算技术指标，按分区存储为Parquet文件
"""

import yfinance as yf
import pandas as pd
import numpy as np
from pathlib import Path
import warnings
from datetime import datetime, timedelta
import time
import os
from typing import List, Dict, Optional, Tuple
import random

warnings.filterwarnings('ignore')

# 设置全局随机种子确保结果可重复
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)

# 01脚本负责数据获取和基本清洗

def load_tickers_from_csv(csv_path: str) -> List[str]:
    """从CSV文件加载股票代码列表"""
    try:
        df = pd.read_csv(csv_path)
        tickers = df['Ticker'].tolist()
        print(f"从{csv_path}加载了{len(tickers)}只股票")
        return tickers
    except Exception as e:
        print(f"加载股票代码失败: {e}")
        return []

def check_existing_data(symbol: str, timeframe: str = '1d') -> Optional[Dict]:
    """检查本地是否存在该股票的数据，返回数据状态信息"""
    data_dir = Path("data")
    partition_dir = data_dir / f"timeframe={timeframe}" / f"symbol={symbol}"
    data_file = partition_dir / "data.parquet"
    
    if not data_file.exists():
        return None
        
    try:
        # 读取现有数据
        df = pd.read_parquet(data_file)
        if df.empty:
            return None
            
        # 获取数据时间范围
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        start_date = df['timestamp'].min()
        end_date = df['timestamp'].max()
        
        return {
            'symbol': symbol,
            'data_points': len(df),
            'start_date': start_date,
            'end_date': end_date,
            'days_old': (datetime.now() - end_date.replace(tzinfo=None)).days,
            'file_path': data_file
        }
    except Exception as e:
        print(f"检查{symbol}数据时出错: {e}")
        return None

def determine_date_range(symbol: str, target_start: str = '2015-01-01') -> Tuple[str, str]:
    """根据已有数据确定需要下载的日期范围"""
    existing_data = check_existing_data(symbol)
    today = datetime.now().strftime('%Y-%m-%d')
    
    if existing_data is None:
        # 没有现有数据，从头开始下载
        print(f"  {symbol}: 首次下载，时间范围 {target_start} 到 {today}")
        return target_start, today
    else:
        # 有现有数据，检查是否需要更新
        last_date = existing_data['end_date'].strftime('%Y-%m-%d')
        days_old = existing_data['days_old']
        
        if days_old <= 1:
            # 数据已是最新，无需更新
            print(f"  {symbol}: 数据已是最新 (最后更新: {last_date})")
            return None, None
        else:
            # 需要增量更新
            # 从最后一天的下一天开始更新，避免重复
            next_day = (existing_data['end_date'] + timedelta(days=1)).strftime('%Y-%m-%d')
            print(f"  {symbol}: 增量更新，时间范围 {next_day} 到 {today}")
            return next_day, today

def fetch_incremental_data(symbol: str, start_date: str, end_date: str, timeframe: str = '1d') -> Optional[pd.DataFrame]:
    """获取指定股票的增量数据"""
    try:
        print(f"    正在下载 {symbol} 数据...")
        
        # 使用yfinance下载数据
        ticker = yf.Ticker(symbol)
        data = ticker.history(
            start=start_date,
            end=end_date,
            interval=timeframe,
            auto_adjust=True,  # 前复权
            prepost=True
        )
        
        if data.empty:
            print(f"    {symbol}: 未获取到数据")
            return None
            
        # 重置索引并标准化列名
        df = data.reset_index()
        df.columns = [col.lower().replace(' ', '_') for col in df.columns]
        
        # 确保有timestamp列
        if 'date' in df.columns:
            df = df.rename(columns={'date': 'timestamp'})
        elif 'datetime' in df.columns:
            df = df.rename(columns={'datetime': 'timestamp'})
            
        # 添加股票代码
        df['symbol'] = symbol
        
        # 检查必要列
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            print(f"    {symbol}: 缺少必要列，跳过")
            return None
            
        print(f"    {symbol}: 成功获取 {len(df)} 条数据")
        return df
        
    except Exception as e:
        print(f"    {symbol}: 获取数据失败 - {e}")
        return None

def merge_and_save_data(symbol: str, new_data: pd.DataFrame, timeframe: str = '1d'):
    """合并新数据和现有数据，然后保存"""
    existing_data = check_existing_data(symbol, timeframe)
    
    if existing_data is None:
        # 没有现有数据，直接处理新数据
        final_data = new_data.copy()
    else:
        # 有现有数据，需要合并
        try:
            old_df = pd.read_parquet(existing_data['file_path'])
            old_df['timestamp'] = pd.to_datetime(old_df['timestamp'])
            new_data['timestamp'] = pd.to_datetime(new_data['timestamp'])
            
            # 统一时区处理 - 将所有时间戳转换为相同时区
            if old_df['timestamp'].dt.tz is not None:
                old_df['timestamp'] = old_df['timestamp'].dt.tz_convert('US/Eastern')
            if new_data['timestamp'].dt.tz is not None:
                new_data['timestamp'] = new_data['timestamp'].dt.tz_convert('US/Eastern')
            
            # 合并数据，去除重复的时间戳
            final_data = pd.concat([old_df, new_data], ignore_index=True)
            final_data = final_data.drop_duplicates(subset=['timestamp'], keep='last')
            final_data = final_data.sort_values('timestamp').reset_index(drop=True)
            
            print(f"    {symbol}: 合并数据，原有 {len(old_df)} 条，新增 {len(new_data)} 条，合并后 {len(final_data)} 条")
        except Exception as e:
            print(f"    {symbol}: 合并数据失败 - {e}，使用新数据")
            final_data = new_data.copy()
    
    # 处理数据，添加技术指标
    final_data = process_single_stock_data(final_data, symbol)
    
    # 保存数据
    save_single_stock_data(final_data, symbol, timeframe)
    
    return final_data

def process_single_stock_data(df: pd.DataFrame, symbol: str) -> pd.DataFrame:
    """
    处理单个股票的基础数据（不包含技术指标）
    
    Args:
        df: 股票数据
        symbol: 股票代码
    
    Returns:
        处理后的基础数据
    """
    # 确保时间戳是日期时间格式
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    # 按时间排序
    df = df.sort_values('timestamp').reset_index(drop=True)
    
    # 确保数据完整性
    required_columns = ['open', 'high', 'low', 'close', 'volume']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"缺少必要列: {col}")
    
    # 基础数据验证和清洗
    df = df.dropna(subset=required_columns)
    
    print(f"    {symbol}: 基础数据处理完成，共{len(df)}条记录")
    
    return df

def save_single_stock_data(df: pd.DataFrame, symbol: str, timeframe: str = '1d'):
    """
    保存单个股票的数据到分区目录
    
    Args:
        df: 处理后的数据
        symbol: 股票代码
        timeframe: 时间频率
    """
    # 创建数据目录
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # 创建分区目录
    partition_dir = data_dir / f"timeframe={timeframe}" / f"symbol={symbol}"
    partition_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存数据
    output_file = partition_dir / "data.parquet"
    df.to_parquet(output_file, index=False)
    
    print(f"    {symbol}: 数据已保存到 {output_file}")
    print(f"    数据点数: {len(df)}，时间范围: {df['timestamp'].min().strftime('%Y-%m-%d')} 到 {df['timestamp'].max().strftime('%Y-%m-%d')}")

def update_stock_data_incremental(ticker_csv_path: str = 'data/tickers_us.csv', max_stocks: int = None):
    """
    增量更新美股数据的主函数
    
    Args:
        ticker_csv_path: 股票代码CSV文件路径
        max_stocks: 最大处理股票数量（测试用）
    """
    print("=== 美股数据增量更新系统 ===")
    print(f"起始日期: 2015-01-01")
    print(f"目标日期: {datetime.now().strftime('%Y-%m-%d')}")
    
    # 加载股票代码列表
    tickers = load_tickers_from_csv(ticker_csv_path)
    if not tickers:
        print("无法加载股票代码列表！")
        return
    
    # 限制处理数量（测试用）
    if max_stocks:
        tickers = tickers[:max_stocks]
        print(f"测试模式: 仅处理前{max_stocks}只股票")
    
    print(f"\n=== 开始处理 {len(tickers)} 只股票 ===")
    
    success_count = 0
    skip_count = 0
    error_count = 0
    
    for i, ticker in enumerate(tickers, 1):
        print(f"\n[{i}/{len(tickers)}] 正在处理 {ticker}...")
        
        try:
            # 确定需要下载的日期范围
            start_date, end_date = determine_date_range(ticker)
            
            if start_date is None:
                # 数据已是最新，跳过
                skip_count += 1
                continue
            
            # 获取新数据
            new_data = fetch_incremental_data(ticker, start_date, end_date)
            
            if new_data is None or new_data.empty:
                print(f"    {ticker}: 无法获取数据，跳过")
                error_count += 1
                continue
            
            # 合并和保存数据
            merge_and_save_data(ticker, new_data)
            success_count += 1
            
            # 添加小延时避免被限流
            if i % 10 == 0:
                print(f"    已处理 {i} 只股票，暂停1秒...")
                time.sleep(1)
            
        except Exception as e:
            print(f"    {ticker}: 处理失败 - {e}")
            error_count += 1
            continue
    
    # 显示结果统计
    print(f"\n=== 更新完成统计 ===")
    print(f"成功更新: {success_count} 只股票")
    print(f"数据已最新: {skip_count} 只股票")
    print(f"处理失败: {error_count} 只股票")
    print(f"总计: {len(tickers)} 只股票")
    
    # 显示数据目录结构
    print(f"\n=== 数据目录结构 ===")
    print("data/")
    print("└── timeframe=1d/")
    
    data_dir = Path("data/timeframe=1d")
    if data_dir.exists():
        symbol_dirs = sorted([d.name for d in data_dir.iterdir() if d.is_dir()])
        for symbol_dir in symbol_dirs[:10]:  # 只显示前10个
            print(f"    ├── {symbol_dir}/")
            print(f"    │   └── data.parquet (基础OHLCV数据)")
        if len(symbol_dirs) > 10:
            print(f"    └── ... 及其他 {len(symbol_dirs) - 10} 只股票")
    

def main():
    """主函数 - 执行增量更新"""
    # 执行增量更新
    # 测试时可以设置 max_stocks=10 仅处理几只股票
    update_stock_data_incremental(max_stocks=None)  # None 表示处理所有股票

if __name__ == "__main__":
    main()