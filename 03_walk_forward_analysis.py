#!/usr/bin/env python3
"""
Walk-Forward Analysis 专业量化建模评估系统
基于样本内/样本外思想的滚动前向分析，解决超参数过拟合问题
"""

import pandas as pd
import numpy as np
import warnings
from pathlib import Path
from datetime import datetime, timedelta
import json
from typing import Dict, List, Tuple, Optional, Any
import joblib
from dataclasses import dataclass
from scipy import stats

# 机器学习相关
import xgboost as xgb
from sklearn.model_selection import TimeSeriesSplit, train_test_split
from sklearn.metrics import classification_report, roc_auc_score, confusion_matrix, roc_curve
from sklearn.feature_selection import SelectKBest, f_classif
from sklearn.preprocessing import StandardScaler

warnings.filterwarnings('ignore')

class FeatureAnalyzer:
    """特征分析器"""
    
    def __init__(self):
        self.feature_groups = {
            '基础价格特征': ['ohlc_avg', 'hl_avg', 'price_change', 'volume_change'],
            '趋势指标': ['macd', 'macd_signal', 'macd_histogram', 'sma_20', 'sma_50', 'ema_12', 'ema_26'],
            '动量指标': ['rsi', 'price_momentum'],
            '波动性指标': ['bb_upper', 'bb_middle', 'bb_lower', 'atr', 'volatility'],
            '成交量指标': ['volume_sma']
        }
    
    def analyze_feature_importance(self, model, feature_names: List[str]) -> pd.DataFrame:
        """分析特征重要性"""
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': model.feature_importances_,
            'group': [self._get_feature_group(feat) for feat in feature_names]
        }).sort_values('importance', ascending=False)
        
        return importance_df
    
    def _get_feature_group(self, feature_name: str) -> str:
        """获取特征所属组别"""
        for group, features in self.feature_groups.items():
            if feature_name in features:
                return group
        return '其他'
    
    def analyze_feature_correlation(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """分析特征相关性"""
        # 特征间相关性
        feature_corr = X.corr()
        
        # 特征与目标的相关性
        target_corr = X.corrwith(y).abs().sort_values(ascending=False)
        
        # 高相关性特征对
        high_corr_pairs = []
        for i in range(len(feature_corr.columns)):
            for j in range(i+1, len(feature_corr.columns)):
                corr_val = abs(feature_corr.iloc[i, j])
                if corr_val > 0.8:
                    high_corr_pairs.append({
                        'feature1': feature_corr.columns[i],
                        'feature2': feature_corr.columns[j],
                        'correlation': corr_val
                    })
        
        return {
            'feature_correlation_matrix': feature_corr.to_dict(),
            'target_correlation': target_corr.to_dict(),
            'high_correlation_pairs': high_corr_pairs
        }

class ModelPerformanceAnalyzer:
    """模型性能分析器"""
    
    def model_performance_analysis(self, model, X_test: pd.DataFrame, y_test: pd.Series) -> Dict:
        """模型性能分析"""
        # 预测
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        y_pred = model.predict(X_test)
        
        # 基础指标
        auc_score = roc_auc_score(y_test, y_pred_proba)
        
        # ROC曲线数据
        fpr, tpr, thresholds = roc_curve(y_test, y_pred_proba)
        
        # 混淆矩阵
        cm = confusion_matrix(y_test, y_pred)
        
        # 不同阈值下的性能
        threshold_analysis = []
        for threshold in np.arange(0.3, 0.8, 0.05):
            y_pred_thresh = (y_pred_proba >= threshold).astype(int)
            tn, fp, fn, tp = confusion_matrix(y_test, y_pred_thresh).ravel()
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            
            threshold_analysis.append({
                'threshold': threshold,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'tp': int(tp), 'fp': int(fp), 'tn': int(tn), 'fn': int(fn)
            })
        
        return {
            'auc_score': auc_score,
            'roc_curve': {'fpr': fpr.tolist(), 'tpr': tpr.tolist(), 'thresholds': thresholds.tolist()},
            'confusion_matrix': cm.tolist(),
            'threshold_analysis': threshold_analysis,
            'classification_report': classification_report(y_test, y_pred, output_dict=True)
        }

@dataclass
class WalkForwardConfig:
    """Walk-Forward分析配置"""
    # 样本内/样本外划分 (可配置时间段，样本外自动获取最新日期)
    in_sample_start: str = "2015-01-01"
    in_sample_end: str = "2023-12-31"       # 样本内：用于策略研发与优化
    out_of_sample_start: str = "2024-01-01" 
    out_of_sample_end: str = datetime.now().strftime("%Y-%m-%d")  # 样本外：自动获取今天日期
    
    # Walk-Forward参数
    train_window_months: int = 24           # 训练窗口：24个月
    test_window_months: int = 6             # 测试窗口：6个月
    step_months: int = 3                    # 滚动步长：3个月
    min_train_samples: int = 1000           # 最小训练样本数
    min_test_samples: int = 100             # 最小测试样本数
    
    # 模型参数
    n_trials_per_fold: int = 50             # 每个折叠的超参数试验次数（增加以提高优化质量）
    cv_folds: int = 5                       # 时间序列交叉验证折数（增加以提高验证稳定性）

class WalkForwardAnalyzer:
    """Walk-Forward分析器"""
    
    def __init__(self, config: WalkForwardConfig = None):
        self.config = config or WalkForwardConfig()
        self.results_dir = Path("logs/walk_forward_results")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # 集成原03的分析器
        self.feature_analyzer = FeatureAnalyzer()
        self.performance_analyzer = ModelPerformanceAnalyzer()
        
    def load_data(self) -> pd.DataFrame:
        """加载训练数据"""
        data_file = Path("model/data/training_data.parquet")
        if not data_file.exists():
            print("❌ 训练数据不存在，请先运行 02_use_feature_store.py")
            return pd.DataFrame()
        
        print("📊 加载训练数据...")
        data = pd.read_parquet(data_file)
        data['date'] = pd.to_datetime(data['date'])
        data = data.sort_values(['date', 'symbol']).reset_index(drop=True)
        
        print(f"  数据形状: {data.shape}")
        print(f"  时间范围: {data['date'].min()} ~ {data['date'].max()}")
        print(f"  股票数量: {data['symbol'].nunique()}")
        
        return data
    
    def generate_walk_forward_windows(self, data: pd.DataFrame) -> List[Dict]:
        """生成Walk-Forward分析窗口"""
        print("\n🔄 生成Walk-Forward分析窗口...")
        
        # 处理时区问题
        data_tz = data['date'].dt.tz if hasattr(data['date'].dt, 'tz') and data['date'].dt.tz is not None else None
        
        # 只在样本内数据上进行滚动分析
        in_sample_start = pd.to_datetime(self.config.in_sample_start)
        in_sample_end = pd.to_datetime(self.config.in_sample_end)
        
        if data_tz:
            in_sample_start = in_sample_start.tz_localize(data_tz)
            in_sample_end = in_sample_end.tz_localize(data_tz)
        
        in_sample_data = data[
            (data['date'] >= in_sample_start) & 
            (data['date'] <= in_sample_end)
        ].copy()
        
        windows = []
        start_date = in_sample_start
        end_date = in_sample_end
        
        current_start = start_date
        
        while True:
            # 训练窗口
            train_end = current_start + pd.DateOffset(months=self.config.train_window_months)
            
            # 测试窗口
            test_start = train_end + pd.DateOffset(days=1)
            test_end = test_start + pd.DateOffset(months=self.config.test_window_months)
            
            # 检查是否超出样本内范围
            if test_end > end_date:
                break
            
            # 提取窗口数据
            train_data = in_sample_data[
                (in_sample_data['date'] >= current_start) & 
                (in_sample_data['date'] <= train_end)
            ].copy()
            
            test_data = in_sample_data[
                (in_sample_data['date'] >= test_start) & 
                (in_sample_data['date'] <= test_end)
            ].copy()
            
            # 验证数据量
            if len(train_data) >= self.config.min_train_samples and len(test_data) >= self.config.min_test_samples:
                window = {
                    'window_id': len(windows) + 1,
                    'train_start': current_start.strftime('%Y-%m-%d'),
                    'train_end': train_end.strftime('%Y-%m-%d'),
                    'test_start': test_start.strftime('%Y-%m-%d'),
                    'test_end': test_end.strftime('%Y-%m-%d'),
                    'train_data': train_data,
                    'test_data': test_data,
                    'train_samples': len(train_data),
                    'test_samples': len(test_data)
                }
                windows.append(window)
                
                print(f"  窗口 {len(windows)}: {current_start.strftime('%Y-%m')} ~ {test_end.strftime('%Y-%m')} "
                      f"(训练: {len(train_data):,}, 测试: {len(test_data):,})")
            
            # 滚动到下一个窗口
            current_start += pd.DateOffset(months=self.config.step_months)
        
        print(f"\n✅ 生成 {len(windows)} 个有效的Walk-Forward窗口")
        return windows
    
    def validate_data_quality(self, data: pd.DataFrame) -> bool:
        """验证数据质量（替代原来的标签生成）"""
        print(f"  🔍 验证数据质量...")
        
        # 检查必要的列
        required_columns = ['target', 'future_return']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            print(f"    ❌ 缺失必要列: {missing_columns}")
            print(f"    💡 请确保运行了02_use_feature_store.py生成target变量")
            return False
        
        # 检查数据质量
        total_rows = len(data)
        na_target = data['target'].isna().sum()
        na_future_return = data['future_return'].isna().sum()
        
        if na_target > 0 or na_future_return > 0:
            print(f"    ⚠️ 发现缺失值: target={na_target}, future_return={na_future_return}")
            return False
        
        # 标签分布统计
        label_stats = data['target'].value_counts()
        pos_ratio = data['target'].mean()
        
        print(f"    ✅ 数据质量验证通过:")
        print(f"    标签分布: {dict(label_stats)}")
        print(f"    正例比例: {pos_ratio:.3f}")
        
        return True
    
    def prepare_features(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """准备特征数据（使用02预处理的数据）"""
        feature_columns = [
            'ohlc_avg', 'hl_avg', 'price_change', 'volume_change',
            'macd', 'macd_signal', 'macd_histogram', 'rsi',
            'sma_20', 'sma_50', 'ema_12', 'ema_26',
            'bb_upper', 'bb_middle', 'bb_lower',
            'atr', 'volume_sma', 'price_momentum', 'volatility'
        ]
        
        # 检查特征列是否存在
        available_features = [col for col in feature_columns if col in data.columns]
        missing_features = [col for col in feature_columns if col not in data.columns]
        
        if missing_features:
            print(f"⚠️ 缺失特征: {missing_features}")
        
        # 验证数据质量
        if not self.validate_data_quality(data):
            raise ValueError("数据质量验证失败，请检查02_use_feature_store.py的输出")
        
        # 提取特征和标签（数据已在02中预处理过）
        X = data[available_features].copy()
        y = data['target']
        
        # 最终安全检查（应该不需要，但保险起见）
        X_values = X.values
        y_values = y.values
        
        if np.any(np.isnan(X_values)) or np.any(np.isinf(X_values)):
            print(f"    ⚠️ 发现异常值，进行最终清理...")
            # 使用更智能的处理方式
            X_df = pd.DataFrame(X_values, columns=available_features)
            for col_idx, col in enumerate(available_features):
                col_data = X_df.iloc[:, col_idx]
                if col_data.isnull().sum() > 0:
                    # 使用中位数填充NaN
                    X_df.iloc[:, col_idx] = col_data.fillna(col_data.median())
                if np.isinf(col_data).sum() > 0:
                    # 使用分位数替换无穷大值
                    finite_values = col_data[np.isfinite(col_data)]
                    if len(finite_values) > 0:
                        upper_bound = finite_values.quantile(0.999)
                        lower_bound = finite_values.quantile(0.001)
                        X_df.iloc[:, col_idx] = col_data.replace([np.inf], upper_bound).replace([-np.inf], lower_bound)
            X_values = X_df.values
        
        return X_values, y_values, available_features
    
    def optimize_hyperparameters(self, X_train: np.ndarray, y_train: np.ndarray) -> Dict:
        """优化超参数 (简化版)"""
        try:
            import optuna
            
            def objective(trial):
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 50, 200),
                    'max_depth': trial.suggest_int('max_depth', 3, 8),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                    'random_state': 42,
                    'objective': 'binary:logistic'
                }
                
                # 时间序列交叉验证
                tscv = TimeSeriesSplit(n_splits=self.config.cv_folds)
                scores = []
                
                for train_idx, val_idx in tscv.split(X_train):
                    X_fold_train, X_fold_val = X_train[train_idx], X_train[val_idx]
                    y_fold_train, y_fold_val = y_train[train_idx], y_train[val_idx]
                    
                    model = xgb.XGBClassifier(**params)
                    model.fit(X_fold_train, y_fold_train)
                    
                    y_pred_proba = model.predict_proba(X_fold_val)[:, 1]
                    score = roc_auc_score(y_fold_val, y_pred_proba)
                    scores.append(score)
                
                return np.mean(scores)
            
            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=self.config.n_trials_per_fold)
            
            return study.best_params
            
        except ImportError:
            # 如果没有optuna，使用默认参数
            print("⚠️ optuna未安装，使用默认参数")
            return {
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'random_state': 42,
                'objective': 'binary:logistic'
            }
    
    def evaluate_single_window(self, window: Dict) -> Dict:
        """评估单个窗口"""
        window_id = window['window_id']
        print(f"\n📊 评估窗口 {window_id}: {window['train_start']} ~ {window['test_end']}")
        
        try:
            # 直接使用预处理的数据（已包含target和清洗过的特征）
            train_data = window['train_data']
            test_data = window['test_data']
            
            # 准备特征
            X_train, y_train, features = self.prepare_features(train_data)
            X_test, y_test, _ = self.prepare_features(test_data)
            
            # 检查标签分布
            if len(np.unique(y_train)) < 2:
                print(f"  ⚠️ 窗口 {window_id}: 训练集标签类别不足，跳过")
                return None
            
            if len(np.unique(y_test)) < 2:
                print(f"  ⚠️ 窗口 {window_id}: 测试集标签类别不足，跳过")
                return None
            
            print(f"  训练集: {X_train.shape[0]:,} 样本, 正例比例: {y_train.mean():.3f}")
            print(f"  测试集: {X_test.shape[0]:,} 样本, 正例比例: {y_test.mean():.3f}")
            
            # 转换为DataFrame用于特征分析
            X_train_df = pd.DataFrame(X_train, columns=features)
            y_train_series = pd.Series(y_train)
            X_test_df = pd.DataFrame(X_test, columns=features)
            y_test_series = pd.Series(y_test)
            
            # 超参数优化
            print(f"  🔧 优化超参数...")
            best_params = self.optimize_hyperparameters(X_train, y_train)
            
            # 训练最终模型
            print(f"  🚀 训练模型...")
            model = xgb.XGBClassifier(**best_params)
            model.fit(X_train, y_train)
            
            # 评估性能
            train_pred_proba = model.predict_proba(X_train)[:, 1]
            test_pred_proba = model.predict_proba(X_test)[:, 1]
            
            train_auc = roc_auc_score(y_train, train_pred_proba)
            test_auc = roc_auc_score(y_test, test_pred_proba)
            
            # 特征重要性分析（只保留核心信息）
            feature_importance_df = self.feature_analyzer.analyze_feature_importance(model, features)
            top_features = feature_importance_df.head(5)[['feature', 'importance', 'group']].to_dict('records')
            
            # 简化的相关性分析（只保留与目标最相关的特征）
            target_corr = X_train_df.corrwith(y_train_series).abs().sort_values(ascending=False)
            top_correlated_features = target_corr.head(5).to_dict()
            
            result = {
                'window_id': window_id,
                'train_period': f"{window['train_start']} ~ {window['train_end']}",
                'test_period': f"{window['test_start']} ~ {window['test_end']}",
                'train_samples': window['train_samples'],
                'test_samples': window['test_samples'],
                'train_auc': train_auc,
                'test_auc': test_auc,
                'overfitting_gap': train_auc - test_auc,
                'best_params': best_params,
                'top_5_features': top_features,
                'top_5_target_correlations': top_correlated_features,
                'model': model  # 保存模型用于最终集成
            }
            
            print(f"  ✅ 窗口 {window_id} 完成 - 训练AUC: {train_auc:.4f}, 测试AUC: {test_auc:.4f}")
            return result
            
        except Exception as e:
            print(f"  ❌ 窗口 {window_id} 评估失败: {str(e)}")
            return None
    
    def run_walk_forward_analysis(self) -> Dict:
        """运行完整的Walk-Forward分析"""
        print("="*60)
        print("🚀 开始Walk-Forward Analysis")
        print("="*60)
        
        # 加载数据
        data = self.load_data()
        if data.empty:
            return {}
        
        # 生成窗口
        windows = self.generate_walk_forward_windows(data)
        if not windows:
            print("❌ 未生成有效窗口")
            return {}
        
        # 逐窗口评估
        print(f"\n🔄 开始逐窗口评估 (共 {len(windows)} 个窗口)...")
        window_results = []
        
        for i, window in enumerate(windows, 1):
            print(f"\n{'='*20} 窗口 {i}/{len(windows)} {'='*20}")
            result = self.evaluate_single_window(window)
            if result:
                window_results.append(result)
        
        # 汇总分析
        summary = self.summarize_walk_forward_results(window_results)
        
        # 样本外最终测试
        oos_result = self.out_of_sample_test(data, window_results)
        
        # 创建评估总结（继承自原03）
        evaluation_summary = self._create_evaluation_summary(summary, oos_result)
        
        # 保存优化参数（继承自原03）
        self._save_optimized_params(window_results, summary)
        
        # 完整结果
        final_results = {
            'config': self.config.__dict__,
            'window_results': window_results,
            'summary': summary,
            'out_of_sample_test': oos_result,
            'evaluation_summary': evaluation_summary,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 保存结果
        self.save_results(final_results)
        
        # 显示结果
        self.display_results(final_results)
        
        return final_results
    
    def summarize_walk_forward_results(self, window_results: List[Dict]) -> Dict:
        """汇总Walk-Forward结果"""
        if not window_results:
            return {}
        
        print(f"\n📊 汇总 {len(window_results)} 个窗口的结果...")
        
        # 提取性能指标
        train_aucs = [r['train_auc'] for r in window_results]
        test_aucs = [r['test_auc'] for r in window_results]
        overfitting_gaps = [r['overfitting_gap'] for r in window_results]
        
        # 聚合特征重要性分析（简化版）
        print(f"  🔍 聚合特征重要性分析...")
        feature_importance_aggregated = self._aggregate_feature_importance_simple(window_results)
        
        # 统计分析
        summary = {
            'total_windows': len(window_results),
            'valid_windows': len(window_results),
            
            # 训练集性能
            'train_auc_mean': np.mean(train_aucs),
            'train_auc_std': np.std(train_aucs),
            'train_auc_min': np.min(train_aucs),
            'train_auc_max': np.max(train_aucs),
            
            # 测试集性能 (最关键)
            'test_auc_mean': np.mean(test_aucs),
            'test_auc_std': np.std(test_aucs),
            'test_auc_min': np.min(test_aucs),
            'test_auc_max': np.max(test_aucs),
            'test_auc_median': np.median(test_aucs),
            
            # 过拟合分析
            'overfitting_gap_mean': np.mean(overfitting_gaps),
            'overfitting_gap_std': np.std(overfitting_gaps),
            'overfitting_gap_max': np.max(overfitting_gaps),
            
            # 稳健性分析
            'performance_stability': 1 - (np.std(test_aucs) / np.mean(test_aucs)),  # 变异系数的倒数
            'consistent_performance_ratio': sum(1 for auc in test_aucs if auc > 0.55) / len(test_aucs),
            
            # 统计显著性
            'performance_above_random': sum(1 for auc in test_aucs if auc > 0.5) / len(test_aucs),
            
            # 简化的聚合分析
            'top_features_across_windows': feature_importance_aggregated
        }
        
        # 计算置信区间
        if len(test_aucs) > 1:
            confidence_interval = stats.t.interval(
                0.95, len(test_aucs)-1,
                loc=np.mean(test_aucs),
                scale=stats.sem(test_aucs)
            )
            summary['test_auc_ci_lower'] = confidence_interval[0]
            summary['test_auc_ci_upper'] = confidence_interval[1]
        
        return summary
    
    def _aggregate_feature_importance_simple(self, window_results: List[Dict]) -> Dict:
        """简化的特征重要性聚合分析"""
        all_importances = {}
        
        for result in window_results:
            top_features = result.get('top_5_features', [])
            for item in top_features:
                feature = item['feature']
                importance = item['importance']
                
                if feature not in all_importances:
                    all_importances[feature] = []
                all_importances[feature].append(importance)
        
        # 计算平均重要性和出现频次
        feature_stats = {}
        for feature, importances in all_importances.items():
            feature_stats[feature] = {
                'mean_importance': np.mean(importances),
                'frequency': len(importances),  # 在多少个窗口中出现在Top5
                'consistency': 1 - (np.std(importances) / np.mean(importances)) if len(importances) > 1 and np.mean(importances) > 0 else 1
            }
        
        # 按平均重要性排序
        sorted_features = sorted(feature_stats.items(), key=lambda x: x[1]['mean_importance'], reverse=True)
        
        return {
            'top_consistent_features': dict(sorted_features[:10]),  # Top 10 最重要且一致的特征
            'feature_frequency_ranking': sorted(feature_stats.items(), key=lambda x: x[1]['frequency'], reverse=True)[:5]
        }
    
    def out_of_sample_test(self, data: pd.DataFrame, window_results: List[Dict]) -> Dict:
        """样本外最终测试"""
        if not window_results:
            return {}
        
        print(f"\n🎯 进行样本外 (Out-of-Sample) 最终测试...")
        print(f"  样本外期间: {self.config.out_of_sample_start} ~ {self.config.out_of_sample_end}")
        
        try:
            # 从测试数据中加载样本外数据
            try:
                test_data = pd.read_parquet("model/data/test_data.parquet")
                
                # 处理时区问题 - 基于测试数据的时区
                test_data_tz = test_data['date'].dt.tz if hasattr(test_data['date'].dt, 'tz') and test_data['date'].dt.tz is not None else None
                
                oos_start = pd.to_datetime(self.config.out_of_sample_start)
                oos_end = pd.to_datetime(self.config.out_of_sample_end)
                is_start = pd.to_datetime(self.config.in_sample_start)
                is_end = pd.to_datetime(self.config.in_sample_end)
                
                if test_data_tz:
                    oos_start = oos_start.tz_localize(test_data_tz)
                    oos_end = oos_end.tz_localize(test_data_tz)
                    is_start = is_start.tz_localize(test_data_tz)
                    is_end = is_end.tz_localize(test_data_tz)
                
                oos_data = test_data[
                    (test_data['date'] >= oos_start) & 
                    (test_data['date'] <= oos_end)
                ].copy()
                
                if oos_data.empty:
                    print("  ⚠️ 样本外数据为空")
                    return {'status': 'no_data'}
                    
            except Exception as e:
                print(f"  ❌ 无法加载测试数据: {e}")
                return {'status': 'no_data'}
            
            # 准备样本内全部数据用于训练最终模型
            # 处理训练数据时区问题
            train_data_tz = data['date'].dt.tz if hasattr(data['date'].dt, 'tz') and data['date'].dt.tz is not None else None
            
            if train_data_tz and not test_data_tz:
                # 如果训练数据有时区，测试数据没有，将测试数据的时区对齐到训练数据
                is_start_train = is_start.tz_localize(train_data_tz) if is_start.tz is None else is_start
                is_end_train = is_end.tz_localize(train_data_tz) if is_end.tz is None else is_end
            elif not train_data_tz and test_data_tz:
                # 如果训练数据没有时区，测试数据有时区，将时区信息去掉
                is_start_train = is_start.tz_localize(None) if is_start.tz is not None else is_start
                is_end_train = is_end.tz_localize(None) if is_end.tz is not None else is_end
            else:
                # 其他情况保持不变
                is_start_train = is_start
                is_end_train = is_end
            
            is_data = data[
                (data['date'] >= is_start_train) & 
                (data['date'] <= is_end_train)
            ].copy()
            
            X_train, y_train, features = self.prepare_features(is_data)
            X_test, y_test, _ = self.prepare_features(oos_data)
            
            print(f"  样本内训练数据: {X_train.shape[0]:,} 样本")
            print(f"  样本外测试数据: {X_test.shape[0]:,} 样本")
            
            # 使用所有窗口中性能最好的参数
            best_window = max(window_results, key=lambda x: x['test_auc'])
            best_params = best_window['best_params']
            
            print(f"  使用窗口 {best_window['window_id']} 的最佳参数 (测试AUC: {best_window['test_auc']:.4f})")
            
            # 训练最终模型
            final_model = xgb.XGBClassifier(**best_params)
            final_model.fit(X_train, y_train)
            
            # 样本外预测
            oos_pred_proba = final_model.predict_proba(X_test)[:, 1]
            oos_auc = roc_auc_score(y_test, oos_pred_proba)
            
            # 与样本内平均性能对比
            is_avg_auc = np.mean([r['test_auc'] for r in window_results])
            performance_gap = oos_auc - is_avg_auc
            
            result = {
                'status': 'success',
                'oos_samples': len(X_test),
                'oos_auc': oos_auc,
                'is_avg_auc': is_avg_auc,
                'performance_gap': performance_gap,
                'best_window_id': best_window['window_id'],
                'best_params': best_params,
                'generalization_assessment': self._assess_generalization(oos_auc, is_avg_auc)
            }
            
            print(f"  ✅ 样本外测试完成")
            print(f"     样本外AUC: {oos_auc:.4f}")
            print(f"     样本内平均AUC: {is_avg_auc:.4f}")
            print(f"     性能差距: {performance_gap:+.4f}")
            
            return result
            
        except Exception as e:
            print(f"  ❌ 样本外测试失败: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def _assess_generalization(self, oos_auc: float, is_avg_auc: float) -> str:
        """评估泛化能力"""
        gap = oos_auc - is_avg_auc
        
        if gap > 0.02:
            return "excellent"  # 样本外表现更好
        elif gap > -0.02:
            return "good"       # 性能基本保持
        elif gap > -0.05:
            return "acceptable" # 轻微衰减
        else:
            return "poor"       # 显著衰减
    
    def save_results(self, results: Dict):
        """保存结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"walk_forward_analysis_{timestamp}.json"
        filepath = self.results_dir / filename
        
        # 转换不可序列化的对象
        serializable_results = results.copy()
        
        # 移除模型对象
        if 'window_results' in serializable_results:
            for window_result in serializable_results['window_results']:
                if 'model' in window_result:
                    del window_result['model']
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 结果已保存至: {filepath}")
    
    def display_results(self, results: Dict):
        """显示分析结果"""
        print("\n" + "="*60)
        print("📈 Walk-Forward Analysis 最终结果")
        print("="*60)
        
        summary = results.get('summary', {})
        oos_result = results.get('out_of_sample_test', {})
        evaluation_summary = results.get('evaluation_summary', {})
        
        # 显示评估总结（继承自原03的风格）
        if evaluation_summary:
            print(f"\n🏆 模型评估总结:")
            for insight in evaluation_summary.get('key_insights', []):
                print(f"  • {insight}")
        
        if summary:
            print(f"\n🔍 样本内 (In-Sample) 滚动验证结果:")
            print(f"  有效窗口数: {summary['total_windows']}")
            print(f"  平均测试AUC: {summary['test_auc_mean']:.4f} ± {summary['test_auc_std']:.4f}")
            print(f"  AUC范围: {summary['test_auc_min']:.4f} ~ {summary['test_auc_max']:.4f}")
            print(f"  中位数AUC: {summary['test_auc_median']:.4f}")
            
            if 'test_auc_ci_lower' in summary:
                print(f"  95%置信区间: [{summary['test_auc_ci_lower']:.4f}, {summary['test_auc_ci_upper']:.4f}]")
            
            print(f"\n📊 稳健性分析:")
            print(f"  性能稳定性: {summary['performance_stability']:.3f} (越接近1越稳定)")
            print(f"  一致性比例: {summary['consistent_performance_ratio']:.1%} (AUC > 0.55的窗口比例)")
            print(f"  超越随机比例: {summary['performance_above_random']:.1%} (AUC > 0.50的窗口比例)")
            
            print(f"\n⚠️ 过拟合分析:")
            print(f"  平均过拟合差距: {summary['overfitting_gap_mean']:.4f}")
            print(f"  最大过拟合差距: {summary['overfitting_gap_max']:.4f}")
            
            # 显示聚合特征分析
            if 'top_features_across_windows' in summary:
                feature_analysis = summary['top_features_across_windows']
                top_features = feature_analysis.get('feature_frequency_ranking', [])
                if top_features:
                    print(f"\n🔥 最一致的Top 5特征 (跨窗口频次):")
                    for i, (feature, stats) in enumerate(top_features[:5], 1):
                        frequency = stats['frequency']
                        importance = stats['mean_importance']
                        print(f"  {i}. {feature}: 平均重要性{importance:.3f}, 出现{frequency}次")
        
        if oos_result.get('status') == 'success':
            print(f"\n🎯 样本外 (Out-of-Sample) 最终测试:")
            print(f"  样本外AUC: {oos_result['oos_auc']:.4f}")
            print(f"  样本内平均AUC: {oos_result['is_avg_auc']:.4f}")
            print(f"  性能差距: {oos_result['performance_gap']:+.4f}")
            print(f"  泛化评估: {oos_result['generalization_assessment']}")
        
        # 最终建议
        self._generate_recommendations(summary, oos_result)
    
    def _create_evaluation_summary(self, summary: Dict, oos_result: Dict) -> Dict:
        """创建模型效果评估总结"""
        if not summary:
            return {}
        
        # 获取关键指标
        avg_auc = summary.get('test_auc_mean', 0)
        oos_auc = oos_result.get('oos_auc', 0) if oos_result.get('status') == 'success' else avg_auc
        stability = summary.get('performance_stability', 0)
        consistency = summary.get('consistent_performance_ratio', 0)
        
        # 效果等级评估（基于样本外或样本内平均AUC）
        evaluation_auc = oos_auc if oos_result.get('status') == 'success' else avg_auc
        
        if evaluation_auc >= 0.75:
            effectiveness_level = "优秀"
            recommendation = "模型表现优秀，Walk-Forward验证显示稳健性强，推荐用于实盘交易"
        elif evaluation_auc >= 0.65:
            effectiveness_level = "良好" 
            recommendation = "模型表现良好，建议小资金验证后扩大规模"
        elif evaluation_auc >= 0.55:
            effectiveness_level = "一般"
            recommendation = "模型表现一般，需要进一步优化特征和参数"
        else:
            effectiveness_level = "较差"
            recommendation = "模型表现较差，不建议用于实际交易"
        
        return {
            'walk_forward_auc_score': avg_auc,
            'out_of_sample_auc_score': oos_auc,
            'final_evaluation_auc': evaluation_auc,
            'effectiveness_level': effectiveness_level,
            'stability_score': stability,
            'consistency_ratio': consistency,
            'recommendation': recommendation,
            'key_insights': [
                f"Walk-Forward平均AUC: {avg_auc:.3f} ({effectiveness_level})",
                f"样本外AUC: {oos_auc:.3f}" if oos_result.get('status') == 'success' else f"样本内验证AUC: {avg_auc:.3f}",
                f"性能稳定性: {stability:.3f} (越接近1越稳定)",
                f"一致性比例: {consistency:.1%} (AUC > 0.55的窗口比例)",
                recommendation
            ]
        }
    
    def _save_optimized_params(self, window_results: List[Dict], summary: Dict):
        """保存优化参数供后续使用"""
        if not window_results:
            return
        
        try:
            # 选择性能最好的窗口的参数
            best_window = max(window_results, key=lambda x: x['test_auc'])
            best_params = best_window['best_params']
            
            # 计算平均性能
            avg_test_auc = summary.get('test_auc_mean', 0)
            
            params_dir = Path("model/optimized_params")
            params_dir.mkdir(parents=True, exist_ok=True)
            
            optimized_params = {
                'walk_forward_analysis': {
                    'best_params': best_params,
                    'best_window_auc': best_window['test_auc'],
                    'best_window_id': best_window['window_id'],
                    'average_test_auc': avg_test_auc,
                    'total_windows_evaluated': len(window_results),
                    'optimization_method': 'walk_forward_analysis'
                },
                'analysis_summary': {
                    'stability_score': summary.get('performance_stability', 0),
                    'consistency_ratio': summary.get('consistent_performance_ratio', 0),
                    'overfitting_gap_mean': summary.get('overfitting_gap_mean', 0)
                },
                'optimization_timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
                'config_used': self.config.__dict__
            }
            
            params_file = params_dir / "optimized_params.json"
            with open(params_file, 'w', encoding='utf-8') as f:
                json.dump(optimized_params, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 优化参数已保存到: {params_file}")
            print(f"  最佳窗口 {best_window['window_id']} 的AUC: {best_window['test_auc']:.4f}")
            print(f"  Walk-Forward平均AUC: {avg_test_auc:.4f}")
            print(f"  参数来源: {len(window_results)} 个窗口的验证结果")
            
        except Exception as e:
            print(f"⚠️ 保存优化参数失败: {str(e)}")
    
    def _generate_recommendations(self, summary: Dict, oos_result: Dict):
        """生成建议"""
        print(f"\n💡 专业建议:")
        
        if not summary:
            print("  - 无有效的分析结果，请检查数据质量")
            return
        
        avg_auc = summary.get('test_auc_mean', 0)
        stability = summary.get('performance_stability', 0)
        consistency = summary.get('consistent_performance_ratio', 0)
        
        # 性能评估
        if avg_auc > 0.65:
            print("  ✅ 模型性能优秀，具备良好的预测能力")
        elif avg_auc > 0.55:
            print("  ⚠️ 模型性能一般，有改进空间")
        else:
            print("  ❌ 模型性能不佳，建议重新设计特征或模型")
        
        # 稳定性评估
        if stability > 0.8 and consistency > 0.7:
            print("  ✅ 策略稳健性良好，在不同市场环境下表现一致")
        elif stability > 0.6:
            print("  ⚠️ 策略稳定性中等，建议增强风险控制")
        else:
            print("  ❌ 策略稳定性差，不建议用于实盘交易")
        
        # 泛化能力评估
        if oos_result.get('status') == 'success':
            generalization = oos_result.get('generalization_assessment')
            if generalization in ['excellent', 'good']:
                print("  ✅ 泛化能力强，策略具备实盘应用潜力")
            elif generalization == 'acceptable':
                print("  ⚠️ 泛化能力可接受，需谨慎应用于实盘")
            else:
                print("  ❌ 泛化能力差，存在严重过拟合风险")

def main():
    """主函数"""
    print("🚀 启动Walk-Forward Analysis专业量化建模评估")
    
    # 配置参数 - 使用默认配置即可
    config = WalkForwardConfig(
        # 时间段配置自动使用类默认值
        train_window_months=24,  # 2年训练窗口
        test_window_months=6,    # 6个月测试窗口
        step_months=3,           # 3个月滚动步长
        n_trials_per_fold=15     # 每个折叠15次超参数试验
    )
    
    print(f"\n📋 分析配置:")
    print(f"  样本内期间: {config.in_sample_start} ~ {config.in_sample_end}")
    print(f"  样本外期间: {config.out_of_sample_start} ~ {config.out_of_sample_end}")
    print(f"  训练窗口: {config.train_window_months} 个月")
    print(f"  测试窗口: {config.test_window_months} 个月")
    print(f"  滚动步长: {config.step_months} 个月")
    
    # 运行分析
    analyzer = WalkForwardAnalyzer(config)
    results = analyzer.run_walk_forward_analysis()
    
    if results:
        print(f"\n🎉 Walk-Forward Analysis 完成！")
    else:
        print(f"\n❌ 分析失败，请检查数据和配置")

if __name__ == "__main__":
    main()