
## 技术报告：基于机器学习的量化交易策略实现方案

### 1.0 系统概述

本报告旨在详细阐述一个基于机器学习的算法交易系统的设计与实现。该系统通过训练一个预测模型来识别短期内具有上涨潜力的股票，并结合一套明确的交易策略与风险管理规则，执行买卖操作以期超越市场基准回报。

**系统核心流程：**

1.  **数据准备**：获取并处理指定股票池的历史OHLC（开盘价、最高价、最低价、收盘价）数据。
2.  **特征工程**：基于历史价格数据，为每个交易日、每支股票计算一系列技术指标作为模型输入特征。
3.  **目标定义**：为每个数据点（即某支股票的某一天）创建一个二元分类标签，用于指示其未来是否满足特定的上涨条件。
4.  **模型训练**：使用准备好的特征和目标数据，训练一个XGBoost分类模型。
5.  **策略执行与回测**：
    * **交易信号生成**：每日利用训练好的模型对最新的市场数据进行预测，生成股票的“上涨概率”。
    * **交易策略**：根据预测概率、排名以及一系列预设规则（如资本分配、仓位管理）来决定每日的买入操作。
    * **风险管理**：应用止盈、止损及最长持有期规则来管理持仓。
    * **回测框架**：在一个模拟环境中执行上述策略，并评估其在历史数据上的表现。

### 2.0 数据管道 (Data Pipeline)

**2.1 数据源**
* **数据类型**: 每日历史价格数据，包括开盘价(Open)、最高价(High)、最低价(Low)、收盘价(Close)。
* **数据调整**: 必须使用**后复权** (Adjusted) 的价格数据，以消除分红和拆股对价格连续性的影响。在实现时，`yfinance`库的`auto_adjust=True`或直接使用`Adj Close`列是合适的。

**2.2 股票池 (Universe)**
* **定义**: 标普500指数(S&P 500)中，按市值排名前100的公司。

**2.3 数据周期 (Time Periods)**
* **训练数据周期**: **2015年1月1日** 至 **2023年12月31日**。
* **回测（测试）数据周期**: **2024年1月1日** 至 今天。

### 3.0 机器学习模型构建

**3.1 目标变量定义 (Target Definition)**

这是整个模型的核心，目标是预测一个二元分类问题。

* **问题定义**: **“在未来 `n` 个交易日内，股价是否上涨超过 `x`%？”**
* **参数**:
    * `n` (时间窗口): **10** 个交易日。
    * `x` (上涨阈值): **5%** (即 `0.05`)。
* **计算公式**:
    1.  对于任意交易日 `t` 的收盘价 `Close_t`，计算其在 `n` 个交易日后的收盘价 `Close_{t+n}`。
    2.  计算未来回报率 `Future_Return = (Close_{t+n} - Close_t) / Close_t`。
    3.  生成目标标签 `Target_t`:
        * 如果 `Future_Return > x`，则 `Target_t = 1`。
        * 否则，`Target_t = 0`。
* **实现注意**: 在pandas中，这通常通过 `close_price.pct_change(periods=n).shift(-n)` 来高效实现。

**3.2 特征工程 (Feature Engineering)**

所有特征均基于历史OHLC数据计算，且不应依赖于绝对价格，而应反映趋势和相对变化。

* **数据基础**: 对每支股票，在每个交易日 `t`，使用 `t` 及之前的历史数据计算。特征工程需要至少过去60个交易日的数据才能完整计算所有指标。
* **特征列表**:
    1.  **相对强弱指数 (RSI)**:
        * 计算周期: 5, 10, 30, 60 天。
        * 公式: `RSI = 100 - (100 / (1 + RS))`，其中 `RS = (前N日平均涨幅) / (前N日平均跌幅)`。
    2.  **平均回报率 (Average Returns)**:
        * 计算周期: 5, 10, 30, 60 天。
        * 类型: 日均、周均、月均回报率。
        * 实现: 可通过 `close_price.pct_change(periods=P).rolling(window=W).mean()` 实现，其中P和W为周期天数。
    3.  **MACD (移动平均收敛散度)**:
        * 计算周期对: (5, 10) — *注意：原文提到了 (5,10) 和 (30,10) 两种，实现时应包含这两种或更标准的 (12, 26)*。
        * 计算特征: **MACD值相对于前一天的变化量**。
        * 公式: `MACD = EMA(fast_period) - EMA(slow_period)`，`Feature = MACD_t - MACD_{t-1}`。
    4.  **价格与均线比率 (Price to MA Ratio)**:
        * 计算周期 `m`: 2, 5, 10, 30, 60 天。
        * 公式: `Ratio = SMA(m)_t / Close_t`，其中`SMA(m)`是m日简单移动平均线。

**3.3 模型选型与训练**

* **模型**: **XGBoost Classifier** (`xgboost.XGBClassifier`)。
* **训练数据**: 将所有100支股票的特征和目标数据**合并**成一个大的训练集。这有助于模型学习通用模式并提高泛化能力。
* **模型参数 (Hyperparameters)**:
    * `objective`: `'binary:logistic'` (用于二元分类)。
    * `eval_metric`: `'logloss'` 或 `'auc'`。
    * `use_label_encoder`: `False` (遵循新版XGBoost的最佳实践)。
    * `base_score`: `0.5` (为避免因数据不平衡导致训练失败，设定一个安全的初始值)。
* **训练流程**:
    1.  为股票池中的每支股票生成特征和目标数据。
    2.  将所有数据合并。
    3.  对齐数据，确保特征和目标数据的索引一致，并移除任何因计算产生的NaN值。
    4.  在合并后的完整数据集上调用 `model.fit(X_train, y_train)`。

### 4.0 交易策略与风险管理

**4.1 信号生成**

1.  每日（在回测的每一天），为股票池中所有股票计算当天的特征。
2.  使用训练好的XGBoost模型，对每支股票的特征进行预测，得到其上涨的概率 `y_pred` (`model.predict_proba(X)[:, 1]`)。

**4.2 买入决策逻辑 (Buy Logic)**

在回测的每个交易日 `t`，按以下顺序决策：

1.  **候选池筛选**:
    * **概率阈值 (`p_low`)**: 筛选出 `y_pred > 0.5` 的股票。
    * **排名选择 (`k`)**: 在满足上述条件的股票中，按 `y_pred` 从高到低排序，选取**前 `k=5` 支**股票作为当日的最终买入候选。
2.  **资本分配 (Capital Allocation)**:
    * **基础投资额**: `(当前总资产 / 过去n天中有交易的天数) * multiplier`。原文中 `n=10`, `multiplier` 可设为 `1.0`。这是一个简化实现，更直接的方法是每次拿出可用现金的一部分。
    * **简化版实现**: `投资额 = 可用现金 / k`。即把当天的可用现金平分给 `k` 支候选股。
3.  **仓位限制 (Position Limits)**:
    * **单日总投资上限 (`daily_amt_limit`)**: 例如，对于10万美元的初始资本，单日投资不超过1.5万美元。
    * **单股持仓上限 (`per_stock_limit`)**: 例如，单只股票的总持仓价值不超过4千美元。在买入前检查，如果买入会导致超过此上限，则**不执行**该笔买入。

**4.3 卖出决策逻辑 (Sell Logic)**

在回测的每个交易日 `t`，对投资组合中的每一笔持仓进行检查：

1.  **止盈 (Take Profit)**:
    * **条件**: `当前价格 >= 买入价 * (1 + x)`
    * `x` (止盈阈值): **10%** (`0.10`)。
2.  **止损 (Stop Loss)**:
    * **条件**: `当前价格 <= 买入价 * (1 - y)`
    * `y` (止损阈值): **5%** (`0.05`)。
3.  **最长持有期 (Time Limit)**:
    * **条件**: `当前日期 - 买入日期 >= n`
    * `n` (最长持有天数): **10** 个交易日。

**如果任何一个卖出条件被触发，则在当天以收盘价卖出全部持仓。**

### 5.0 回测框架与性能评估

**5.1 回测框架设计**

需要一个事件驱动的框架，按时间顺序模拟每日操作。

* **核心循环**: 遍历回测周期内的每一个交易日。
* **状态变量**:
    * `cash`: 当前可用现金。
    * `positions`: 一个字典或对象，记录当前持有的股票、份额、买入价格和买入日期。
    * `portfolio_value`: 每日的总资产（`cash` + 所有持仓的当前市值）。
* **每日操作流程**:
    1.  更新 `portfolio_value` 的期初值。
    2.  **执行卖出逻辑**: 遍历 `positions`，检查是否触发卖出条件。如果触发，更新 `cash` 和 `positions`。
    3.  **执行买入逻辑**: 根据当天的模型预测和策略规则，决定是否买入新股。如果买入，更新 `cash` 和 `positions`。
    4.  记录当日结束时的 `portfolio_value`。

**5.2 性能指标**

在回测结束后，基于每日的 `portfolio_value` 时间序列，计算以下指标：

1.  **总回报率 (Total Return)**: `(最终资产 / 初始资产) - 1`。
2.  **夏普比率 (Sharpe Ratio)**: `(策略年化回报率 - 无风险利率) / 策略年化波动率`。
3.  **索提诺比率 (Sortino Ratio)**: 类似于夏普比率，但只考虑下行波动率。
4.  **最大回撤 (Maximum Drawdown)**: 投资组合净值从最高点到最低点的最大跌幅。

**5.3 基准对比 (Benchmark)**

为了评估策略效果，应与一个基准进行比较。

* **基准策略**: 等权重买入并持有(Buy and Hold)股票池中的所有股票。
* **计算**: 在回测期初，将初始资本平均分配给股票池中的所有股票，并持有至期末。计算该基准策略的每日资产净值和最终性能指标。