#!/bin/bash
# 量化交易系统完整流程执行脚本
# 功能：一键运行从数据获取到交易信号生成的完整流程
# 用法：./run_pipeline.sh

set -e  # 任何命令失败时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v uv &> /dev/null; then
        log_error "uv 未安装，请先安装 uv"
        exit 1
    fi
    
    if [ ! -f "pyproject.toml" ]; then
        log_error "未找到 pyproject.toml，请确保在项目根目录运行"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 运行单个脚本
run_script() {
    local script_name=$1
    local description=$2
    local script_args=${3:-"--auto"}  # 默认参数为--auto
    
    log_info "开始运行: $description"
    echo "=================================================="
    
    if uv run python "$script_name" $script_args; then
        log_success "$description 完成"
    else
        log_error "$description 失败"
        exit 1
    fi
    
    echo ""
}

# 检查结果文件
check_results() {
    log_info "检查结果文件..."
    
    # 检查关键输出文件
    local files_to_check=(
        "model/data/training_data.parquet"
        "model/data/test_data.parquet"
        "model/optimized_params/optimized_params.json"
        "model/saved_models/xgboost_model.pkl"
    )
    
    for file in "${files_to_check[@]}"; do
        if [ -f "$file" ]; then
            log_success "✅ $file 存在"
        else
            log_warning "⚠️ $file 不存在"
        fi
    done
    
    # 检查日志目录
    local log_dirs=(
        "logs/feature_results"
        "logs/model_results"
        "logs/walk_forward_results"
        "logs/trading_signals"
    )
    
    for dir in "${log_dirs[@]}"; do
        if [ -d "$dir" ] && [ "$(ls -A "$dir")" ]; then
            log_success "✅ $dir 包含结果文件"
        else
            log_warning "⚠️ $dir 为空或不存在"
        fi
    done
}

# 显示性能摘要
show_performance() {
    log_info "性能摘要..."
    
    # 尝试从最新的结果文件中提取性能指标
    local latest_model_result=$(find logs/model_results -name "*.json" -type f -exec ls -t {} + | head -n 1)
    
    if [ -f "$latest_model_result" ]; then
        log_success "最新模型结果: $latest_model_result"
        
        # 使用 uv run python 提取关键指标
        uv run python -c "
import json
try:
    with open('$latest_model_result', 'r') as f:
        data = json.load(f)
    
    training = data.get('training_results', {})
    strategy = data.get('strategy_results', {})
    
    print('📊 模型性能:')
    print(f'  验证AUC: {training.get(\"val_auc\", \"N/A\"):.4f}')
    print(f'  训练准确率: {training.get(\"train_accuracy\", \"N/A\"):.4f}')
    print(f'  验证准确率: {training.get(\"val_accuracy\", \"N/A\"):.4f}')
    
    print('💹 策略表现:')
    print(f'  总收益率: {strategy.get(\"total_return\", \"N/A\"):.2%}')
    print(f'  夏普比率: {strategy.get(\"sharpe_ratio\", \"N/A\"):.4f}')
    print(f'  最大回撤: {strategy.get(\"max_drawdown\", \"N/A\"):.2%}')
    print(f'  总交易次数: {strategy.get(\"total_trades\", \"N/A\")}')
    
except Exception as e:
    print(f'无法解析结果文件: {e}')
        "
    else
        log_warning "未找到模型结果文件"
    fi
}

# 主流程
main() {
    echo "=================================================="
    echo "🚀 量化交易系统完整流程（质量筛选250只股票训练）"
    echo "=================================================="
    
    # 检查依赖
    check_dependencies
    
    # 创建必要目录
    mkdir -p logs/{feature_results,model_results,walk_forward_results,trading_signals}
    mkdir -p model/{data,optimized_params}
    mkdir -p model/saved_models
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 运行完整流程（专业量化建模流程）
    run_script "01_fetch_and_save_data.py" "01 数据获取（517只股票）"
    run_script "02_use_feature_store.py" "02 特征管理和数据准备（质量筛选250只股票）" "--auto-quality"
    run_script "03_walk_forward_analysis.py" "03 Walk-Forward Analysis专业验证"
    run_script "04_model_training.py" "04 模型训练和回测（使用Walk-Forward最佳参数）"
    run_script "05_trading_signal_system.py" "05 交易信号生成"
    
    # 计算总耗时
    end_time=$(date +%s)
    total_time=$((end_time - start_time))
    
    echo "=================================================="
    log_success "🎉 完整流程执行完成！"
    echo "=================================================="
    
    # 检查结果
    check_results
    
    # 显示性能摘要
    show_performance
    
    echo "=================================================="
    log_info "📊 执行统计:"
    echo "  总耗时: ${total_time}秒"
    echo "  执行时间: $(date)"
    echo ""
    log_info "📋 下一步建议:"
    echo "  1. 查看 Dashboard: python dashboard.py"
    echo "  2. 检查交易信号: ls logs/trading_signals/"
    echo "  3. 分析模型结果: ls logs/model_results/"
    echo "=================================================="
}

# 运行主流程
main "$@"