#!/usr/bin/env python3
"""
科学实验框架：逐个测试特征对模型性能的影响
基于186.37%收益率版本进行渐进式优化
"""

import subprocess
import json
import time
from pathlib import Path
from datetime import datetime

class FeatureExperiment:
    """特征实验管理器"""
    
    def __init__(self):
        self.results = {}
        self.baseline_performance = None
        
    def run_experiment(self, experiment_name: str, description: str):
        """运行单个实验"""
        print(f"\n{'='*60}")
        print(f"🧪 开始实验: {experiment_name}")
        print(f"📝 描述: {description}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            # 1. 运行特征工程
            print("步骤1: 运行特征工程...")
            result = subprocess.run([
                "uv", "run", "python", "02_use_feature_store.py", f"--{experiment_name}"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                print(f"❌ 特征工程失败: {result.stderr}")
                return None
            
            # 2. 运行模型训练和回测
            print("步骤2: 运行模型训练和回测...")
            result = subprocess.run([
                "uv", "run", "python", "04_model_training.py", "--auto"
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode != 0:
                print(f"❌ 模型训练失败: {result.stderr}")
                return None
            
            # 3. 解析结果
            performance = self.parse_latest_results()
            if performance:
                elapsed_time = time.time() - start_time
                performance['experiment_name'] = experiment_name
                performance['description'] = description
                performance['elapsed_time'] = elapsed_time
                
                print(f"✅ 实验完成!")
                print(f"📊 性能结果:")
                print(f"   总收益率: {performance['total_return']:.2%}")
                print(f"   最大回撤: {performance['max_drawdown']:.2%}")
                print(f"   夏普比率: {performance['sharpe_ratio']:.4f}")
                print(f"   用时: {elapsed_time:.1f}秒")
                
                return performance
            else:
                print("❌ 无法解析结果")
                return None
                
        except subprocess.TimeoutExpired:
            print("❌ 实验超时")
            return None
        except Exception as e:
            print(f"❌ 实验失败: {e}")
            return None
    
    def parse_latest_results(self):
        """解析最新的回测结果"""
        results_dir = Path("logs/model_results")
        if not results_dir.exists():
            return None
        
        # 找到最新的结果文件
        result_files = list(results_dir.glob("trading_results_*.json"))
        if not result_files:
            return None
        
        latest_file = max(result_files, key=lambda x: x.stat().st_mtime)
        
        try:
            with open(latest_file, 'r') as f:
                data = json.load(f)
            
            strategy_results = data.get('strategy_results', {})
            return {
                'total_return': strategy_results.get('total_return', 0),
                'max_drawdown': strategy_results.get('max_drawdown', 0),
                'sharpe_ratio': strategy_results.get('sharpe_ratio', 0),
                'total_trades': strategy_results.get('total_trades', 0),
                'final_value': strategy_results.get('final_value', 0)
            }
        except Exception as e:
            print(f"解析结果文件失败: {e}")
            return None
    
    def compare_with_baseline(self, performance):
        """与基准版本对比"""
        if not self.baseline_performance:
            return "基准版本"
        
        return_improvement = (performance['total_return'] - self.baseline_performance['total_return']) / self.baseline_performance['total_return']
        sharpe_improvement = (performance['sharpe_ratio'] - self.baseline_performance['sharpe_ratio']) / self.baseline_performance['sharpe_ratio']
        drawdown_change = (performance['max_drawdown'] - self.baseline_performance['max_drawdown']) / abs(self.baseline_performance['max_drawdown'])
        
        return {
            'return_improvement': return_improvement,
            'sharpe_improvement': sharpe_improvement,
            'drawdown_change': drawdown_change
        }
    
    def run_all_experiments(self):
        """运行所有实验"""
        experiments = [
            ("baseline", "基准版本 - 原有39个特征"),
            ("exp1_rsi21", "实验1 - 添加RSI21中期指标"),
            ("exp2_momentum20", "实验2 - 添加20日动量指标"),
            ("exp3_trend_consistency", "实验3 - 添加趋势一致性指标"),
            ("exp4_price_ratios", "实验4 - 添加价格比率特征")
        ]
        
        print("🚀 开始科学实验序列")
        print(f"实验数量: {len(experiments)}")
        print(f"预计用时: {len(experiments) * 15} 分钟")
        
        for exp_name, description in experiments:
            performance = self.run_experiment(exp_name, description)
            
            if performance:
                self.results[exp_name] = performance
                
                # 设置基准
                if exp_name == "baseline":
                    self.baseline_performance = performance
                
                # 对比分析
                comparison = self.compare_with_baseline(performance)
                if comparison != "基准版本":
                    print(f"📈 相对基准改进:")
                    print(f"   收益率: {comparison['return_improvement']:+.2%}")
                    print(f"   夏普比率: {comparison['sharpe_improvement']:+.2%}")
                    print(f"   最大回撤: {comparison['drawdown_change']:+.2%}")
            
            print(f"\n⏱️ 休息10秒后继续下一个实验...")
            time.sleep(10)
        
        # 生成最终报告
        self.generate_final_report()
    
    def generate_final_report(self):
        """生成最终实验报告"""
        print(f"\n{'='*80}")
        print("📊 实验总结报告")
        print(f"{'='*80}")
        
        if not self.results:
            print("❌ 没有有效的实验结果")
            return
        
        # 按夏普比率排序
        sorted_results = sorted(self.results.items(), key=lambda x: x[1]['sharpe_ratio'], reverse=True)
        
        print(f"{'实验名称':<20} {'收益率':<10} {'回撤':<10} {'夏普比率':<10} {'交易次数':<8}")
        print("-" * 70)
        
        for exp_name, perf in sorted_results:
            print(f"{exp_name:<20} {perf['total_return']:>8.2%} {perf['max_drawdown']:>8.2%} {perf['sharpe_ratio']:>8.4f} {perf['total_trades']:>6}")
        
        # 找出最佳实验
        best_exp = sorted_results[0]
        print(f"\n🏆 最佳实验: {best_exp[0]}")
        print(f"   总收益率: {best_exp[1]['total_return']:.2%}")
        print(f"   夏普比率: {best_exp[1]['sharpe_ratio']:.4f}")
        print(f"   最大回撤: {best_exp[1]['max_drawdown']:.2%}")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"logs/experiment_report_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📁 详细结果已保存到: {report_file}")

if __name__ == "__main__":
    experiment = FeatureExperiment()
    experiment.run_all_experiments()
