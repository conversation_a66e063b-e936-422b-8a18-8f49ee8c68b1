#!/usr/bin/env python3
"""
完整交易信号生成系统
基于机器学习模型的实时交易决策系统
"""

import pandas as pd
import numpy as np
import warnings
from pathlib import Path
from datetime import datetime, timedelta
import json
from typing import Dict, List, Tuple, Optional, Any
import joblib
from dataclasses import dataclass, asdict
from enum import Enum
from sklearn.metrics import classification_report, confusion_matrix
import random

warnings.filterwarnings('ignore')

# 设置全局随机种子确保结果可重复
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)

class SignalType(Enum):
    """交易信号类型"""
    BUY = "BUY"
    SELL = "SELL" 
    HOLD = "HOLD"

class RiskLevel(Enum):
    """风险等级"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"

@dataclass
class TradingSignal:
    """交易信号"""
    symbol: str
    signal_type: SignalType
    confidence: float  # 置信度 0-1
    price: float      # 当前价格
    target_price: float  # 目标价格
    stop_loss: float  # 止损价格
    risk_level: RiskLevel
    reasoning: str    # 推理依据
    timestamp: datetime
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['signal_type'] = self.signal_type.value
        data['risk_level'] = self.risk_level.value
        data['timestamp'] = self.timestamp.strftime('%Y-%m-%d %H:%M:%S')
        return data

@dataclass
class PortfolioAction:
    """投资组合动作"""
    action_type: str  # buy, sell, hold, rebalance
    symbol: str
    quantity: int
    price: float
    reasoning: str
    expected_return: float
    max_loss: float
    holding_period: int  # 预期持有天数
    
class TradingRiskManager:
    """交易风险管理器"""
    
    def __init__(self, max_position_size: float = 0.1, max_daily_loss: float = 0.02):
        """
        max_position_size: 单一持仓最大占比
        max_daily_loss: 单日最大损失比例
        """
        self.max_position_size = max_position_size
        self.max_daily_loss = max_daily_loss
        
    def calculate_position_size(self, portfolio_value: float, confidence: float, 
                              volatility: float) -> float:
        """计算仓位大小"""
        # Kelly公式改进版
        base_size = self.max_position_size * confidence
        volatility_adjustment = max(0.5, 1 - volatility)  # 高波动性降低仓位
        
        return min(base_size * volatility_adjustment, self.max_position_size)
    
    def assess_risk_level(self, volatility: float, confidence: float) -> RiskLevel:
        """评估风险等级"""
        risk_score = volatility * (1 - confidence)
        
        if risk_score < 0.15:
            return RiskLevel.LOW
        elif risk_score < 0.3:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.HIGH

class ModelPredictor:
    """模型预测器"""
    
    def __init__(self, model_path: str = "model/saved_models/xgboost_model.pkl"):
        self.model = None
        self.feature_columns = None
        self.load_model(model_path)
    
    def load_model(self, model_path: str):
        """加载模型"""
        try:
            self.model = joblib.load(model_path)
            print(f"✅ 模型加载成功: {model_path}")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
    
    def predict_probability(self, features: pd.DataFrame) -> np.ndarray:
        """预测概率"""
        if self.model is None:
            raise ValueError("模型未加载")
            
        # 数据清理
        clean_features = features.copy()
        for col in clean_features.columns:
            clean_features[col] = clean_features[col].replace([np.inf, -np.inf], np.nan)
        clean_features = clean_features.fillna(0)
        
        return self.model.predict_proba(clean_features.values)[:, 1]

class ThresholdOptimizer:
    """阈值优化器 - 基于历史数据自动校准交易信号阈值"""
    
    def __init__(self):
        self.predictor = ModelPredictor()
    
    def analyze_probability_distribution(self) -> Dict:
        """分析模型概率分布"""
        print("=== 分析模型概率分布 ===")
        
        try:
            # 加载历史测试数据
            test_data = pd.read_parquet("model/data/test_data.parquet")
            
            # 准备特征
            feature_columns = [
                'ohlc_avg', 'hl_avg', 'price_change', 'volume_change',
                'macd', 'macd_signal', 'macd_histogram', 'rsi',
                'sma_20', 'sma_50', 'ema_12', 'ema_26',
                'bb_upper', 'bb_middle', 'bb_lower',
                'atr', 'volume_sma', 'price_momentum', 'volatility'
            ]
            
            available_features = [col for col in feature_columns if col in test_data.columns]
            X = test_data[available_features].fillna(0)
            
            # 预测概率
            probabilities = self.predictor.predict_probability(X)
            y_true = test_data['target'].values
            
            # 计算分布统计
            prob_stats = {
                'mean': float(np.mean(probabilities)),
                'median': float(np.median(probabilities)),
                'std': float(np.std(probabilities)),
                'min': float(np.min(probabilities)),
                'max': float(np.max(probabilities)),
                'percentiles': {
                    '10': float(np.percentile(probabilities, 10)),
                    '25': float(np.percentile(probabilities, 25)),
                    '50': float(np.percentile(probabilities, 50)),
                    '75': float(np.percentile(probabilities, 75)),
                    '90': float(np.percentile(probabilities, 90)),
                    '95': float(np.percentile(probabilities, 95))
                }
            }
            
            print(f"概率分布统计:")
            print(f"  均值: {prob_stats['mean']:.3f}")
            print(f"  中位数: {prob_stats['median']:.3f}")
            print(f"  标准差: {prob_stats['std']:.3f}")
            print(f"  范围: [{prob_stats['min']:.3f}, {prob_stats['max']:.3f}]")
            print(f"  百分位数: P25={prob_stats['percentiles']['25']:.3f}, P75={prob_stats['percentiles']['75']:.3f}")
            
            return prob_stats
            
        except Exception as e:
            print(f"❌ 概率分布分析失败: {e}")
            return {}
    
    def optimize_thresholds(self, target_buy_ratio: float = 0.08, target_sell_ratio: float = 0.12) -> Dict:
        """优化阈值以达到目标信号比例"""
        print(f"=== 优化交易阈值 (目标买入:{target_buy_ratio:.1%}, 卖出:{target_sell_ratio:.1%}) ===")
        
        try:
            # 加载测试数据
            test_data = pd.read_parquet("model/data/test_data.parquet")
            
            # 准备特征
            feature_columns = [
                'ohlc_avg', 'hl_avg', 'price_change', 'volume_change',
                'macd', 'macd_signal', 'macd_histogram', 'rsi',
                'sma_20', 'sma_50', 'ema_12', 'ema_26',
                'bb_upper', 'bb_middle', 'bb_lower',
                'atr', 'volume_sma', 'price_momentum', 'volatility'
            ]
            
            available_features = [col for col in feature_columns if col in test_data.columns]
            X = test_data[available_features].fillna(0)
            
            # 预测概率
            probabilities = self.predictor.predict_probability(X)
            y_true = test_data['target'].values
            
            # 基于百分位数优化阈值
            buy_threshold = np.percentile(probabilities, (1 - target_buy_ratio) * 100)
            sell_threshold = np.percentile(probabilities, target_sell_ratio * 100)
            
            # 确保阈值合理性（基于分析结果优化）
            buy_threshold = max(buy_threshold, 0.35)  # 提高最低买入阈值以改善准确率
            sell_threshold = min(sell_threshold, 0.25)  # 最高卖出阈值
            
            # 验证优化效果
            buy_signals = (probabilities >= buy_threshold).sum()
            sell_signals = (probabilities <= sell_threshold).sum()
            hold_signals = len(probabilities) - buy_signals - sell_signals
            
            total_samples = len(probabilities)
            actual_buy_ratio = buy_signals / total_samples
            actual_sell_ratio = sell_signals / total_samples
            actual_hold_ratio = hold_signals / total_samples
            
            # 计算信号质量
            buy_mask = probabilities >= buy_threshold
            sell_mask = probabilities <= sell_threshold
            
            buy_accuracy = np.mean(y_true[buy_mask]) if buy_signals > 0 else 0
            sell_accuracy = 1 - np.mean(y_true[sell_mask]) if sell_signals > 0 else 0
            
            results = {
                'optimized_thresholds': {
                    'buy_threshold': float(buy_threshold),
                    'sell_threshold': float(sell_threshold),
                    'confidence_threshold': float((buy_threshold + sell_threshold) / 2)
                },
                'signal_distribution': {
                    'buy_signals': int(buy_signals),
                    'sell_signals': int(sell_signals),
                    'hold_signals': int(hold_signals),
                    'buy_ratio': float(actual_buy_ratio),
                    'sell_ratio': float(actual_sell_ratio),
                    'hold_ratio': float(actual_hold_ratio)
                },
                'signal_quality': {
                    'buy_accuracy': float(buy_accuracy),
                    'sell_accuracy': float(sell_accuracy),
                    'overall_samples': int(total_samples)
                }
            }
            
            print(f"✅ 阈值优化完成:")
            print(f"  买入阈值: {buy_threshold:.3f}")
            print(f"  卖出阈值: {sell_threshold:.3f}")
            print(f"  信号分布: 买入{actual_buy_ratio:.1%}, 卖出{actual_sell_ratio:.1%}, 持有{actual_hold_ratio:.1%}")
            print(f"  信号质量: 买入准确率{buy_accuracy:.1%}, 卖出准确率{sell_accuracy:.1%}")
            
            return results
            
        except Exception as e:
            print(f"❌ 阈值优化失败: {e}")
            return {}

class TradingSignalGenerator:
    """交易信号生成器"""
    
    def __init__(self, auto_optimize_thresholds: bool = True):
        self.predictor = ModelPredictor()
        self.risk_manager = TradingRiskManager()
        self.threshold_optimizer = ThresholdOptimizer()
        
        # 优化后的信号参数（基于准确率分析）
        self.buy_threshold = 0.55   # 买入概率阈值（提高质量）
        self.sell_threshold = 0.20  # 卖出概率阈值（减少过度卖出）
        self.confidence_threshold = 0.5  # 高置信度阈值（放宽以增加信号）
        
        # 自动优化阈值
        if auto_optimize_thresholds:
            self._auto_optimize_thresholds()
    
    def _auto_optimize_thresholds(self):
        """自动优化阈值"""
        try:
            optimization_results = self.threshold_optimizer.optimize_thresholds()
            if optimization_results:
                optimized = optimization_results['optimized_thresholds']
                self.buy_threshold = optimized['buy_threshold']
                self.sell_threshold = optimized['sell_threshold']
                self.confidence_threshold = optimized['confidence_threshold']
                print(f"✅ 阈值已自动优化: 买入{self.buy_threshold:.3f}, 卖出{self.sell_threshold:.3f}")
        except Exception as e:
            print(f"⚠️ 阈值自动优化失败，使用默认值: {e}")
    
    def update_thresholds(self, buy_threshold: float = None, sell_threshold: float = None, confidence_threshold: float = None):
        """手动更新阈值"""
        if buy_threshold is not None:
            self.buy_threshold = buy_threshold
        if sell_threshold is not None:
            self.sell_threshold = sell_threshold
        if confidence_threshold is not None:
            self.confidence_threshold = confidence_threshold
        
        print(f"阈值已更新: 买入{self.buy_threshold:.3f}, 卖出{self.sell_threshold:.3f}, 置信度{self.confidence_threshold:.3f}")
        
    def load_latest_features(self) -> pd.DataFrame:
        """加载最新特征数据"""
        print("=== 加载最新特征数据 ===")
        
        # 尝试加载测试数据中的最新数据
        try:
            test_data = pd.read_parquet("model/data/test_data.parquet")
            latest_date = test_data['date'].max()
            latest_data = test_data[test_data['date'] == latest_date]
            
            print(f"最新数据日期: {latest_date}")
            print(f"股票数量: {len(latest_data)}")
            
            return latest_data
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return pd.DataFrame()
    
    def prepare_features(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """准备特征"""
        feature_columns = [
            'ohlc_avg', 'hl_avg', 'price_change', 'volume_change',
            'macd', 'macd_signal', 'macd_histogram', 'rsi',
            'sma_20', 'sma_50', 'ema_12', 'ema_26',
            'bb_upper', 'bb_middle', 'bb_lower',
            'atr', 'volume_sma', 'price_momentum', 'volatility'
        ]
        
        available_features = [col for col in feature_columns if col in data.columns]
        
        if not available_features:
            raise ValueError("没有可用的特征列")
        
        X = data[available_features].copy()
        
        # 数据清理
        for col in available_features:
            X[col] = X[col].replace([np.inf, -np.inf], np.nan)
        X = X.fillna(X.mean())
        
        return X, available_features
    
    def generate_signals(self, market_data: pd.DataFrame) -> List[TradingSignal]:
        """生成交易信号"""
        print("=== 生成交易信号 ===")
        
        if market_data.empty:
            print("❌ 没有数据可供分析")
            return []
        
        signals = []
        
        try:
            # 准备特征
            X, feature_columns = self.prepare_features(market_data)
            
            # 预测概率
            probabilities = self.predictor.predict_probability(X)
            
            # 为每只股票生成信号
            for idx, (_, row) in enumerate(market_data.iterrows()):
                symbol = row['symbol']
                current_price = row['close']
                prob = probabilities[idx]
                
                # 计算技术指标用于风险评估
                volatility = row.get('volatility', 0.02)
                atr = row.get('atr', current_price * 0.02)
                
                # 生成信号
                signal = self._create_signal(
                    symbol=symbol,
                    probability=prob,
                    current_price=current_price,
                    volatility=volatility,
                    atr=atr,
                    market_data=row
                )
                
                if signal:
                    signals.append(signal)
            
            # 按置信度排序
            signals.sort(key=lambda x: x.confidence, reverse=True)
            
            print(f"生成 {len(signals)} 个交易信号")
            return signals
            
        except Exception as e:
            print(f"❌ 信号生成失败: {e}")
            return []
    
    def _create_signal(self, symbol: str, probability: float, current_price: float,
                      volatility: float, atr: float, market_data: pd.Series) -> Optional[TradingSignal]:
        """创建交易信号"""
        
        # 判断信号类型
        if probability >= self.buy_threshold:
            signal_type = SignalType.BUY
            confidence = probability
            target_price = current_price * (1 + 0.05)  # 5%目标收益
            stop_loss = current_price * (1 - 0.03)     # 3%止损
            reasoning = f"模型预测上涨概率{probability:.3f}, "
            
            # 添加技术分析理由
            if market_data.get('rsi', 50) < 30:
                reasoning += "RSI超卖, "
            if market_data.get('price_change', 0) > 0:
                reasoning += "价格上升趋势, "
            if market_data.get('macd', 0) > market_data.get('macd_signal', 0):
                reasoning += "MACD金叉, "
                
        elif probability <= self.sell_threshold:
            signal_type = SignalType.SELL
            confidence = 1 - probability
            target_price = current_price * (1 - 0.05)  # 5%目标下跌
            stop_loss = current_price * (1 + 0.03)     # 3%止损
            reasoning = f"模型预测下跌概率{1-probability:.3f}, "
            
            # 添加技术分析理由
            if market_data.get('rsi', 50) > 70:
                reasoning += "RSI超买, "
            if market_data.get('price_change', 0) < 0:
                reasoning += "价格下降趋势, "
            if market_data.get('macd', 0) < market_data.get('macd_signal', 0):
                reasoning += "MACD死叉, "
        else:
            # 信号不够强，建议持有
            return None
        
        # 评估风险等级
        risk_level = self.risk_manager.assess_risk_level(volatility, confidence)
        
        # 如果置信度太低，不生成信号（调整至与新阈值匹配）
        if confidence < 0.40:
            return None
        
        return TradingSignal(
            symbol=symbol,
            signal_type=signal_type,
            confidence=confidence,
            price=current_price,
            target_price=target_price,
            stop_loss=stop_loss,
            risk_level=risk_level,
            reasoning=reasoning.rstrip(', '),
            timestamp=datetime.now()
        )

class PortfolioManager:
    """投资组合管理器"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.available_cash = initial_capital
        self.positions = {}  # {symbol: {shares: int, cost_basis: float}}
        self.risk_manager = TradingRiskManager()
        
    def create_portfolio_actions(self, signals: List[TradingSignal]) -> List[PortfolioAction]:
        """基于信号创建投资组合动作"""
        actions = []
        
        # 当前投资组合价值
        portfolio_value = self.calculate_portfolio_value(signals)
        
        # 处理买入信号
        buy_signals = [s for s in signals if s.signal_type == SignalType.BUY]
        buy_signals = buy_signals[:5]  # 最多买入5只股票
        
        for signal in buy_signals:
            if signal.confidence < 0.6:  # 置信度太低
                continue
                
            # 计算仓位大小
            position_size = self.risk_manager.calculate_position_size(
                portfolio_value, signal.confidence, 0.02  # 假设2%波动率
            )
            
            investment_amount = portfolio_value * position_size
            
            if investment_amount > self.available_cash:
                investment_amount = self.available_cash * 0.9  # 保留10%现金
            
            if investment_amount < 1000:  # 最小投资金额
                continue
                
            shares = int(investment_amount / signal.price)
            
            if shares > 0:
                action = PortfolioAction(
                    action_type="buy",
                    symbol=signal.symbol,
                    quantity=shares,
                    price=signal.price,
                    reasoning=signal.reasoning,
                    expected_return=(signal.target_price - signal.price) / signal.price,
                    max_loss=(signal.price - signal.stop_loss) / signal.price,
                    holding_period=10  # 预期持有10天
                )
                actions.append(action)
        
        return actions
    
    def calculate_portfolio_value(self, current_signals: List[TradingSignal]) -> float:
        """计算投资组合价值"""
        # 简化版本，实际应该查询当前持仓的市场价值
        return self.available_cash + sum(pos['shares'] * pos['cost_basis'] 
                                       for pos in self.positions.values())

class TradingReport:
    """交易报告生成器"""
    
    def __init__(self):
        pass
    
    def generate_daily_report(self, signals: List[TradingSignal], 
                            actions: List[PortfolioAction]) -> Dict:
        """生成每日交易报告"""
        
        # 信号统计
        buy_signals = [s for s in signals if s.signal_type == SignalType.BUY]
        sell_signals = [s for s in signals if s.signal_type == SignalType.SELL]
        
        # 风险分析
        risk_distribution = {}
        for risk_level in RiskLevel:
            count = len([s for s in signals if s.risk_level == risk_level])
            risk_distribution[risk_level.value] = count
        
        # 推荐动作
        buy_actions = [a for a in actions if a.action_type == "buy"]
        
        report = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'market_analysis': {
                'total_signals': len(signals),
                'buy_signals': len(buy_signals),
                'sell_signals': len(sell_signals),
                'average_confidence': np.mean([s.confidence for s in signals]) if signals else 0,
                'risk_distribution': risk_distribution
            },
            'recommended_actions': {
                'buy_recommendations': [
                    {
                        'symbol': action.symbol,
                        'quantity': action.quantity,
                        'price': action.price,
                        'expected_return': f"{action.expected_return:.2%}",
                        'max_loss': f"{action.max_loss:.2%}",
                        'reasoning': action.reasoning
                    }
                    for action in buy_actions
                ],
                'total_investment': sum(a.quantity * a.price for a in buy_actions)
            },
            'top_opportunities': [
                {
                    'symbol': signal.symbol,
                    'signal_type': signal.signal_type.value,
                    'confidence': f"{signal.confidence:.3f}",
                    'current_price': signal.price,
                    'target_price': signal.target_price,
                    'potential_return': f"{(signal.target_price - signal.price) / signal.price:.2%}",
                    'risk_level': signal.risk_level.value,
                    'reasoning': signal.reasoning
                }
                for signal in signals[:10]  # Top 10
            ]
        }
        
        return report

class TradingSystem:
    """完整交易系统"""
    
    def __init__(self):
        self.signal_generator = TradingSignalGenerator()
        self.portfolio_manager = PortfolioManager()
        self.report_generator = TradingReport()
        
    def run_daily_analysis(self) -> Dict:
        """运行每日分析"""
        print("=== 开始每日交易分析 ===")
        print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 1. 加载最新市场数据
            market_data = self.signal_generator.load_latest_features()
            
            if market_data.empty:
                print("❌ 没有可用的市场数据")
                return {'error': '没有可用的市场数据'}
            
            # 2. 生成交易信号
            signals = self.signal_generator.generate_signals(market_data)
            
            # 3. 创建投资组合动作
            actions = self.portfolio_manager.create_portfolio_actions(signals)
            
            # 4. 生成交易报告
            report = self.report_generator.generate_daily_report(signals, actions)
            
            # 5. 保存结果
            self._save_daily_results(report, signals, actions)
            
            # 6. 显示关键结果
            self._display_summary(report)
            
            return report
            
        except Exception as e:
            print(f"❌ 系统运行失败: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}
    
    def _save_daily_results(self, report: Dict, signals: List[TradingSignal], 
                           actions: List[PortfolioAction]):
        """保存每日结果"""
        results_dir = Path("logs/trading_signals")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存完整报告
        report_file = results_dir / f"daily_report_{timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self._process_for_json(report), f, indent=2, ensure_ascii=False)
        
        # 保存详细信号
        signals_file = results_dir / f"signals_{timestamp}.json"
        signals_data = [self._process_for_json(signal.to_dict()) for signal in signals]
        with open(signals_file, 'w', encoding='utf-8') as f:
            json.dump(signals_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 结果已保存:")
        print(f"  报告: {report_file}")
        print(f"  信号: {signals_file}")
    
    def _process_for_json(self, obj):
        """处理对象以便JSON序列化"""
        if isinstance(obj, dict):
            return {k: self._process_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._process_for_json(v) for v in obj]
        elif isinstance(obj, (np.float32, np.float64, np.int32, np.int64)):
            return float(obj) if 'float' in str(type(obj)) else int(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (pd.Timestamp, datetime)):
            return obj.strftime('%Y-%m-%d %H:%M:%S') if hasattr(obj, 'strftime') else str(obj)
        elif hasattr(obj, '__dict__'):
            return self._process_for_json(obj.__dict__)
        else:
            return str(obj) if not isinstance(obj, (str, int, float, bool, type(None))) else obj
    
    def _display_summary(self, report: Dict):
        """显示结果摘要"""
        print("\n" + "="*60)
        print("📊 每日交易分析结果")
        print("="*60)
        
        analysis = report['market_analysis']
        print(f"🎯 市场信号: {analysis['total_signals']} 个")
        print(f"   买入信号: {analysis['buy_signals']} 个")
        print(f"   卖出信号: {analysis['sell_signals']} 个")
        print(f"   平均置信度: {analysis['average_confidence']:.3f}")
        
        actions = report['recommended_actions']
        print(f"\n💰 推荐操作: {len(actions['buy_recommendations'])} 个买入机会")
        print(f"   总投资金额: ${actions['total_investment']:,.2f}")
        
        print(f"\n🔥 Top 5 投资机会:")
        for i, opp in enumerate(report['top_opportunities'][:5], 1):
            print(f"   {i}. {opp['symbol']}: {opp['signal_type']} (置信度{opp['confidence']}, "
                  f"潜在收益{opp['potential_return']}, 风险{opp['risk_level']})")
        
        print("\n💡 基于以上分析进行投资决策")

def test_threshold_optimization():
    """测试阈值优化功能"""
    print("=== 交易阈值优化测试 ===")
    
    # 创建阈值优化器
    optimizer = ThresholdOptimizer()
    
    # 分析概率分布
    prob_stats = optimizer.analyze_probability_distribution()
    
    # 优化阈值（提高质量，减少买入信号数量）
    optimization_results = optimizer.optimize_thresholds(target_buy_ratio=0.05, target_sell_ratio=0.15)
    
    # 保存优化结果
    if optimization_results:
        results_dir = Path("logs/threshold_optimization")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"threshold_optimization_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'probability_distribution': prob_stats,
                'optimization_results': optimization_results,
                'timestamp': timestamp
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 优化结果已保存: {results_file}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--optimize-thresholds":
        # 阈值优化模式
        test_threshold_optimization()
        return
    
    print("=== 完整交易信号生成系统 ===")
    
    # 创建交易系统
    trading_system = TradingSystem()
    
    # 运行每日分析
    result = trading_system.run_daily_analysis()
    
    if 'error' not in result:
        print("\n✅ 交易信号生成完成！")
        print("📈 可基于生成的信号进行交易决策")
        print("\n💡 提示: 使用 --optimize-thresholds 参数可优化交易阈值")
    else:
        print(f"\n❌ 分析失败: {result['error']}")

if __name__ == "__main__":
    main()