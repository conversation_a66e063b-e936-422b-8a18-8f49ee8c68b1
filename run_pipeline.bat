@echo off
REM 量化交易系统完整流程执行脚本 (Windows批处理版本)
REM 功能：一键运行从数据获取到交易信号生成的完整流程
REM 用法：run_pipeline.bat [quick] - 添加quick参数启用快速模式
REM       run_pipeline.bat      - 标准完整流程

setlocal enabledelayedexpansion

REM 检查是否是快速模式
set QUICK_MODE=false
if "%1"=="quick" (
    set QUICK_MODE=true
    echo ===================================================================
    echo ⚡ 量化交易系统快速测试模式 (Windows版本)
    echo ===================================================================
) else (
    echo ===================================================================
    echo 🚀 量化交易系统完整流程 (Windows版本)
    echo ===================================================================
)

REM 检查uv是否安装
where uv >nul 2>nul
if errorlevel 1 (
    echo ❌ 错误: uv 未安装，请先安装 uv
    echo 安装方法: powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
    pause
    exit /b 1
)

REM 检查项目文件
if not exist "pyproject.toml" (
    echo ❌ 错误: 未找到 pyproject.toml，请确保在项目根目录运行
    pause
    exit /b 1
)

echo ✅ 依赖检查完成

REM 创建必要目录
if not exist "logs" mkdir logs
if not exist "logs\feature_results" mkdir logs\feature_results
if not exist "logs\model_results" mkdir logs\model_results
if not exist "logs\trading_signals" mkdir logs\trading_signals
if not exist "logs\automation" mkdir logs\automation
if not exist "model" mkdir model
if not exist "model\data" mkdir model\data
if not exist "model\optimized_params" mkdir model\optimized_params
if not exist "saved_models" mkdir saved_models

echo ✅ 目录创建完成

REM 记录开始时间
set start_time=%time%

REM 设置Windows兼容的环境变量
set PYTHONIOENCODING=utf-8
set PYTHONUNBUFFERED=1

echo.
echo ===================================================================
echo 步骤 1/5: 数据获取和特征计算
echo ===================================================================
uv run python 01_fetch_and_save_data.py --auto
if errorlevel 1 (
    echo ❌ 01 数据获取失败，终止流程
    pause
    exit /b 1
)
echo ✅ 01 数据获取完成

echo.
echo ===================================================================
echo 步骤 2/5: 特征管理和数据准备
echo ===================================================================
if "%QUICK_MODE%"=="true" (
    echo 💡 快速模式：使用标准模式（100只股票）
    uv run python 02_use_feature_store.py --auto
    if errorlevel 1 (
        echo ❌ 02 特征管理失败，终止测试
        pause
        exit /b 1
    ) else (
        echo ✅ 02 特征管理完成（快速模式）
    )
) else (
    echo 💡 使用高质量模式（250只优质股票）以获得更好的模型性能
    uv run python 02_use_feature_store.py --auto-quality
    if errorlevel 1 (
        echo ⚠️ 02 特征管理失败，尝试标准模式
        uv run python 02_use_feature_store.py --auto
        if errorlevel 1 (
            echo ❌ 02 特征管理完全失败，但继续执行
            set failed_02=1
        ) else (
            echo ✅ 02 特征管理完成（标准模式）
        )
    ) else (
        echo ✅ 02 特征管理完成（高质量模式）
    )
)

echo.
echo ===================================================================
echo 步骤 3/5: 模型优化和超参数调优
echo ===================================================================
if "%QUICK_MODE%"=="true" (
    echo 💡 快速模式：跳过耗时的Walk-Forward分析
    echo ✅ 03 模型优化跳过（快速模式）
) else (
    uv run python 03_walk_forward_analysis.py --auto
    if errorlevel 1 (
        echo ⚠️ 03 模型优化失败，但继续执行
        set failed_03=1
    ) else (
        echo ✅ 03 模型优化完成
    )
)

echo.
echo ===================================================================
echo 步骤 4/5: 模型训练和回测
echo ===================================================================
if "%QUICK_MODE%"=="true" (
    echo 💡 快速模式：训练模型 + 轻量级回测验证
    uv run python 04_model_training.py --auto
    if errorlevel 1 (
        echo ❌ 04 模型训练失败，终止测试
        pause
        exit /b 1
    ) else (
        echo ✅ 04 模型训练和验证完成（快速模式）
        echo 💡 快速模式已包含基本回测验证，可查看模型效果
    )
) else (
    uv run python 04_model_training.py --auto
    if errorlevel 1 (
        echo ⚠️ 04 模型训练失败，但继续执行
        set failed_04=1
    ) else (
        echo ✅ 04 模型训练完成
    )
)

echo.
echo ===================================================================
echo 步骤 5/5: 交易信号生成
echo ===================================================================
uv run python 05_trading_signal_system.py --auto
if errorlevel 1 (
    echo ⚠️ 05 交易信号失败
    set failed_05=1
) else (
    echo ✅ 05 交易信号完成
)

REM 记录结束时间
set end_time=%time%

echo.
echo ===================================================================
echo 🎉 流程执行完成！
echo ===================================================================

REM 检查结果文件
echo 📋 结果文件检查:
if exist "model\data\training_data.parquet" (
    echo ✅ 训练数据已生成
) else (
    echo ⚠️ 训练数据未找到
)

if exist "model\data\test_data.parquet" (
    echo ✅ 测试数据已生成
) else (
    echo ⚠️ 测试数据未找到
)

if exist "model\optimized_params\optimized_params.json" (
    echo ✅ 优化参数已生成
) else (
    echo ⚠️ 优化参数未找到
)

if exist "saved_models\xgboost_model.pkl" (
    echo ✅ 模型文件已生成
) else (
    echo ⚠️ 模型文件未找到
)

echo.
echo 📊 执行统计:
echo   开始时间: %start_time%
echo   结束时间: %end_time%
echo   操作系统: Windows

echo.
echo 📋 下一步建议:
if "%QUICK_MODE%"=="true" (
    echo   🔥 快速测试完成！如需完整功能请运行: run_pipeline.bat
    echo   1. 查看训练结果: dir model\data
    echo   2. 检查交易信号: dir logs\trading_signals
    echo   3. 完整流程测试: run_pipeline.bat
) else (
    echo   1. 查看 Dashboard: uv run python dashboard.py
    echo   2. 检查交易信号: dir logs\trading_signals
    echo   3. 分析模型结果: dir logs\model_results
    echo   4. 快速测试: run_pipeline.bat quick
)
echo ===================================================================

pause